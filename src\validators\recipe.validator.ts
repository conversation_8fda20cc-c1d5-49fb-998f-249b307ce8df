import { celebrate, Joi, Segments } from "celebrate";
import {
  RecipeStatus,
  RecipeServingMethod,
  RecipeYieldUnit,
  RecipeServeIn,
  RecipeComplexityLevel,
} from "../models/Recipe";

const createRecipeValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_title: Joi.string().max(100).required(),
        recipe_public_title: Joi.string().max(100).allow(null, "").optional(),
        recipe_description: Joi.string().allow(null, "").optional(),
        recipe_preparation_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_cook_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        has_recipe_public_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false),
        has_recipe_private_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false),
        is_cost_manual: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false),
        recipe_status: Joi.string()
          .valid(...Object.values(RecipeStatus))
          .default(RecipeStatus.draft),
        recipe_serve_in: Joi.string()
          .valid(...Object.values(RecipeServeIn))
          .allow(null, "")
          .optional(),
        recipe_complexity_level: Joi.string()
          .valid(...Object.values(RecipeComplexityLevel))
          .allow(null, "")
          .optional(),
        recipe_garnish: Joi.string().allow(null, "").optional(),
        recipe_head_chef_tips: Joi.string().allow(null, "").optional(),
        recipe_foh_tips: Joi.string().allow(null, "").optional(),
        recipe_impression: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_yield: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        recipe_yield_unit: Joi.string()
          .valid(...Object.values(RecipeYieldUnit))
          .allow(null, "")
          .optional(),
        recipe_total_portions: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        recipe_single_portion_size: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        recipe_serving_method: Joi.string()
          .valid(...Object.values(RecipeServingMethod))
          .allow(null, "")
          .optional(),
        recipe_placeholder: Joi.alternatives()
          .try(Joi.number(), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        // Vitamin and mineral content fields
        vitamin_a: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        vitamin_c: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        calcium: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        iron: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        // Cooking and preparation method flags
        is_ingredient_cooking_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false)
          .optional(),
        is_preparation_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .default(false)
          .optional(),
        categories: Joi.alternatives()
          .try(Joi.array().items(Joi.number()).min(1), Joi.string())
          .optional(),
        // Type-wise attributes (like ingredients)
        nutrition_attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                unit_of_measure: Joi.string()
                  .max(100)
                  .allow(null, "")
                  .optional(),
                unit: Joi.number().allow(null).optional(),
                description: Joi.string().allow(null, "").optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        allergen_attributes: Joi.alternatives()
          .try(
            Joi.object({
              contains: Joi.array().items(Joi.number()).optional(),
              may_contain: Joi.array().items(Joi.number()).optional(),
            }).custom((value, helpers) => {
              // Validation: ensure no allergen is in both contains and may_contain
              const contains = value.contains || [];
              const mayContain = value.may_contain || [];

              const duplicates = contains.filter((id: number) =>
                mayContain.includes(id)
              );
              if (duplicates.length > 0) {
                return helpers.error("any.custom", {
                  message: `Allergen IDs ${duplicates.join(", ")} cannot be in both 'contains' and 'may_contain' lists`,
                });
              }

              return value;
            }),
            Joi.string()
          )
          .optional(),
        may_contain_allergens: Joi.alternatives()
          .try(Joi.array().items(Joi.number()), Joi.string())
          .optional(),
        cuisine_attributes: Joi.alternatives()
          .try(Joi.array().items(Joi.number()), Joi.string())
          .optional(),
        dietary_attributes: Joi.alternatives()
          .try(Joi.array().items(Joi.number()), Joi.string())
          .optional(),
        haccp_attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().integer().positive().required(),
                description: Joi.string().allow(null, "").optional(),
                use_default: Joi.alternatives()
                  .try(Joi.boolean(), Joi.string().valid("true", "false"))
                  .optional()
                  .default(false),
              })
            ),
            Joi.string()
          )
          .optional(),
        // Legacy support for single attributes array
        attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                unit_of_measure: Joi.string()
                  .max(100)
                  .allow(null, "")
                  .optional(),
                unit: Joi.number().allow(null).optional(),
                description: Joi.string().allow(null, "").optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        ingredients: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                quantity: Joi.number().min(0).allow(null).optional(),
                measure: Joi.number().allow(null).optional(),
                wastage: Joi.number().min(0).max(100).allow(null).optional(),
                cost: Joi.number().min(0).allow(null).optional(),
                cooking_method: Joi.number().allow(null).optional(),
                preparation_method: Joi.number().allow(null).optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        steps: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                order: Joi.number().min(1).required(),
                description: Joi.string().allow(null, "").optional(),
                item_id: Joi.number().allow(null).optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        resources: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                type: Joi.string().valid("item", "link").required(),
                item_id: Joi.string().when("type", {
                  is: "item",
                  then: Joi.required(),
                  otherwise: Joi.allow(null),
                }),
                item_link: Joi.string().when("type", {
                  is: "link",
                  then: Joi.required(),
                  otherwise: Joi.allow(null),
                }),
                item_link_type: Joi.string()
                  .valid("image", "video", "pdf", "audio", "youtube", "link")
                  .when("type", {
                    is: "link",
                    then: Joi.required(),
                    otherwise: Joi.allow(null),
                  }),
              })
            ),
            Joi.string()
          )
          .optional(),
        // File upload fields (handled by multer)
        recipeFiles: Joi.any().optional(),
        stepImages: Joi.any().optional(),
        recipePlaceholder: Joi.any().optional(),
      })
      .unknown(true)
      .options({ allowUnknown: true, stripUnknown: false }),
  });

const updateRecipeValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object()
      .keys({
        recipe_title: Joi.string().max(100).optional(),
        recipe_public_title: Joi.string().max(100).allow(null, "").optional(),
        recipe_description: Joi.string().allow(null, "").optional(),
        recipe_preparation_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_cook_time: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        has_recipe_public_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional()
          .messages({
            "alternatives.match": "has_recipe_public_visibility must be a boolean (true/false) or string ('true'/'false')"
          }),
        has_recipe_private_visibility: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional()
          .messages({
            "alternatives.match": "has_recipe_private_visibility must be a boolean (true/false) or string ('true'/'false')"
          }),
        is_cost_manual: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional()
          .messages({
            "alternatives.match": "is_cost_manual must be a boolean (true/false) or string ('true'/'false')"
          }),
        recipe_status: Joi.string()
          .valid(...Object.values(RecipeStatus))
          .optional(),
        recipe_serve_in: Joi.string()
          .valid(...Object.values(RecipeServeIn))
          .allow(null, "")
          .optional(),
        recipe_complexity_level: Joi.string()
          .valid(...Object.values(RecipeComplexityLevel))
          .allow(null, "")
          .optional(),
        recipe_garnish: Joi.string().allow(null, "").optional(),
        recipe_head_chef_tips: Joi.string().allow(null, "").optional(),
        recipe_foh_tips: Joi.string().allow(null, "").optional(),
        recipe_impression: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        recipe_yield: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        recipe_yield_unit: Joi.string()
          .valid(...Object.values(RecipeYieldUnit))
          .allow(null, "")
          .optional(),
        recipe_total_portions: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        recipe_single_portion_size: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        recipe_serving_method: Joi.string()
          .valid(...Object.values(RecipeServingMethod))
          .allow(null, "")
          .optional(),
        recipe_placeholder: Joi.alternatives()
          .try(Joi.number(), Joi.string().pattern(/^\d+$/))
          .allow(null)
          .optional(),
        // Vitamin and mineral content fields
        vitamin_a: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        vitamin_c: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        calcium: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        iron: Joi.alternatives()
          .try(Joi.number().min(0), Joi.string().pattern(/^\d*\.?\d+$/))
          .allow(null)
          .optional(),
        // Cooking and preparation method flags
        is_ingredient_cooking_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional()
          .messages({
            "alternatives.match": "is_ingredient_cooking_method must be a boolean (true/false) or string ('true'/'false')"
          }),
        is_preparation_method: Joi.alternatives()
          .try(Joi.boolean(), Joi.string().valid("true", "false"))
          .optional()
          .messages({
            "alternatives.match": "is_preparation_method must be a boolean (true/false) or string ('true'/'false')"
          }),
        categories: Joi.alternatives()
          .try(Joi.array().items(Joi.number()), Joi.string())
          .optional(),
        // Type-wise attributes (like ingredients)
        nutrition_attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                unit_of_measure: Joi.string()
                  .max(100)
                  .allow(null, "")
                  .optional(),
                unit: Joi.number().allow(null).optional(),
                description: Joi.string().allow(null, "").optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        allergen_attributes: Joi.alternatives()
          .try(
            Joi.object({
              contains: Joi.array().items(Joi.number()).optional(),
              may_contain: Joi.array().items(Joi.number()).optional(),
            }).custom((value, helpers) => {
              // Validation: ensure no allergen is in both contains and may_contain
              const contains = value.contains || [];
              const mayContain = value.may_contain || [];

              const duplicates = contains.filter((id: number) =>
                mayContain.includes(id)
              );
              if (duplicates.length > 0) {
                return helpers.error("any.custom", {
                  message: `Allergen IDs ${duplicates.join(", ")} cannot be in both 'contains' and 'may_contain' lists`,
                });
              }

              return value;
            }),
            Joi.string()
          )
          .optional(),
        may_contain_allergens: Joi.alternatives()
          .try(Joi.array().items(Joi.number()), Joi.string())
          .optional(),
        cuisine_attributes: Joi.alternatives()
          .try(Joi.array().items(Joi.number()), Joi.string())
          .optional(),
        dietary_attributes: Joi.alternatives()
          .try(Joi.array().items(Joi.number()), Joi.string())
          .optional(),
        haccp_attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().integer().positive().required(),
                description: Joi.string().allow(null, "").optional(),
                use_default: Joi.alternatives()
                  .try(Joi.boolean(), Joi.string().valid("true", "false"))
                  .optional()
                  .default(false),
              })
            ),
            Joi.string()
          )
          .optional(),
        // Legacy support for single attributes array
        attributes: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                unit_of_measure: Joi.string()
                  .max(100)
                  .allow(null, "")
                  .optional(),
                unit: Joi.number().allow(null).optional(),
                description: Joi.string().allow(null, "").optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        ingredients: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                id: Joi.number().required(),
                quantity: Joi.number().min(0).allow(null).optional(),
                measure: Joi.number().allow(null).optional(),
                wastage: Joi.number().min(0).max(100).allow(null).optional(),
                cost: Joi.number().min(0).allow(null).optional(),
                cooking_method: Joi.number().allow(null).optional(),
                preparation_method: Joi.number().allow(null).optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        steps: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                order: Joi.number().min(1).required(),
                description: Joi.string().allow(null, "").optional(),
                item_id: Joi.number().allow(null).optional(),
              })
            ),
            Joi.string()
          )
          .optional(),
        resources: Joi.alternatives()
          .try(
            Joi.array().items(
              Joi.object({
                type: Joi.string().valid("item", "link").required(),
                item_id: Joi.string().when("type", {
                  is: "item",
                  then: Joi.required(),
                  otherwise: Joi.allow(null),
                }),
                item_link: Joi.string().when("type", {
                  is: "link",
                  then: Joi.required(),
                  otherwise: Joi.allow(null),
                }),
                item_link_type: Joi.string()
                  .valid("image", "video", "pdf", "audio", "youtube", "link")
                  .when("type", {
                    is: "link",
                    then: Joi.required(),
                    otherwise: Joi.allow(null),
                  }),
              })
            ),
            Joi.string()
          )
          .optional(),
        // File upload fields (handled by multer)
        recipeFiles: Joi.any().optional(),
        stepImages: Joi.any().optional(),
        recipePlaceholder: Joi.any().optional(),
      })
      .unknown(true)
      .options({ allowUnknown: true, stripUnknown: false }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getRecipeValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.alternatives()
        .try(Joi.number().positive(), Joi.string().min(1))
        .required()
        .messages({
          "alternatives.match":
            "Recipe identifier must be a positive number (ID) or a non-empty string (slug)",
          "any.required": "Recipe identifier is required",
        }),
    }),
  });

const deleteRecipeValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getRecipesListValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      organization_id: Joi.string().optional(),
      recipe_status: Joi.string()
        .valid(...Object.values(RecipeStatus))
        .optional(),
      recipe_complexity_level: Joi.string()
        .valid(...Object.values(RecipeComplexityLevel))
        .optional(),
      category: Joi.string().optional(),
      categories: Joi.string().optional(), // For fast API
      attribute: Joi.string().optional(),
      allergens: Joi.string().optional(), // For fast API
      exclude_allergen: Joi.string().optional(), // New filter to exclude allergens
      dietary: Joi.string().optional(), // For fast API
      cuisine: Joi.string().optional(), // For fast API
      ingredient: Joi.string().optional(),
      search: Joi.string().optional(),
      portion_cost_min: Joi.number().min(0).optional(), // For fast API
      portion_cost_max: Joi.number().min(0).optional(), // For fast API
      cooking_time_min: Joi.number().min(0).optional(), // New filter for minimum cooking time
      cooking_time_max: Joi.number().min(0).optional(), // New filter for maximum cooking time
      bookmark: Joi.string().valid("true", "false").optional(), // For fast API
      sort_by: Joi.string()
        .valid(
          "recipe_title",
          "alphabetical",
          "title",
          "portion_cost",
          "created_at",
          "updated_at",
          "recipe_status",
          "recipe_preparation_time",
          "recipe_cook_time",
          "recipe_complexity_level"
        )
        .optional(),
      sort_order: Joi.string().valid("ASC", "DESC").optional(),
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
      download: Joi.string().valid("excel", "csv").optional(),
      visibility: Joi.string().valid("public", "private").optional(),
      // New filter to get recipes by user
      exclude_ingredient: Joi.string().optional(), // New filter to exclude ingredients,
      organization_slug: Joi.string().optional(),


    }),
  });

const addBookmarkValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const impressionValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.alternatives()
        .try(Joi.number().positive(), Joi.string().min(1))
        .required()
        .messages({
          "alternatives.match":
            "Recipe identifier must be a positive number (ID) or a non-empty string (slug)",
          "any.required": "Recipe identifier is required",
        }),
    }),
  });

const duplicateRecipeValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

const getHistoryValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
    [Segments.QUERY]: Joi.object().keys({
      page: Joi.number().min(1).optional(),
      limit: Joi.number().min(1).max(100).optional(),
      action: Joi.string().optional(),
      user_id: Joi.number().optional(),
      filter: Joi.string().valid('activity', 'history').optional(),
    }),
  });

const exportRecipeValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
    [Segments.QUERY]: Joi.object().keys({
      format: Joi.string().valid("excel", "csv", "pdf").optional(),
    }),
  });

const publishRecipeValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
    [Segments.BODY]: Joi.object().keys({
      has_recipe_public_visibility: Joi.boolean().optional(),
      has_recipe_private_visibility: Joi.boolean().optional(),
    }),
  });

const makeRecipePublicValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
    [Segments.BODY]: Joi.object().keys({
      has_recipe_public_visibility: Joi.boolean().required(),
      has_recipe_private_visibility: Joi.boolean().optional(),
    }),
  });

// Assignment validators
const manageAssignmentsValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      recipe_id: Joi.number().integer().min(1).required(),
      user_ids: Joi.array()
        .items(Joi.number().integer().min(1))
        .required()
        .messages({
          "array.base": "user_ids must be an array",
          "any.required": "user_ids is required",
        }),
    }),
  });

const getAssignedRecipesValidator = () =>
  celebrate({
    [Segments.QUERY]: Joi.object().keys({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      status: Joi.string().valid("active", "inactive").optional(),
      recipe_status: Joi.string()
        .valid(...Object.values(RecipeStatus))
        .optional(),
      search: Joi.string().max(100).optional(),
      sort_by: Joi.string()
        .valid("assigned_date", "recipe_title", "created_at")
        .default("assigned_date"),
      sort_order: Joi.string().valid("asc", "desc").default("desc"),
    }),
  });

const validateImportFile = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      organization_id: Joi.string().allow(null, "").optional(),
    }),
  });

export default {
  createRecipeValidator,
  updateRecipeValidator,
  getRecipeValidator,
  deleteRecipeValidator,
  getRecipesListValidator,
  addBookmarkValidator,
  impressionValidator,
  duplicateRecipeValidator,
  getHistoryValidator,
  exportRecipeValidator,
  publishRecipeValidator,
  makeRecipePublicValidator,
  manageAssignmentsValidator,
  getAssignedRecipesValidator,
  validateImportFile,
};
