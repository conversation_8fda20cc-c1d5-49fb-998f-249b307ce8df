import { Op, QueryTypes } from "sequelize";
import { RecipeHistory, RecipeHistoryAction } from "../models/RecipeHistory";
import { Category } from "../models/Category";
import { FoodAttributes } from "../models/FoodAttributes";
import { Ingredient } from "../models/Ingreditant";
import { Item } from "../models/Item";
import { User } from "../models/User";
import { RecipeSteps } from "../models/RecipeSteps";
import { RecipeResources } from "../models/RecipeResources";
import { sequelize } from "../models/index";
import * as ExcelJS from "exceljs";
import * as Handlebars from "handlebars";
import {
  getUserLiterals,
  getEnhancedUserLiterals,
  getOrganizationLogo,
  getOrgName,
} from "./common";
import {
  getBulkRecipeHighlights,
  getRecipeHighlight,
  getSimpleBulkRecipeHighlights,
  getSimpleRecipeHighlight,
} from "./recipe-highlight.helper";
import {
  areRecipeCostsOutdated,
  areRecipeNutritionValuesOutdated,
} from "./timestamp.helper";
import settingsService from "../services/settings.service";

/**
 * Convert database boolean values (0/1) to JavaScript booleans (true/false)
 * @param data - Object or array containing data to convert
 * @param booleanFields - Array of field names that should be converted to boolean
 * @returns Data with boolean fields properly converted
 */
export const convertBooleanFields = (data: any, booleanFields: string[]): any => {
  if (!data) return data;

  // Handle arrays
  if (Array.isArray(data)) {
    return data.map(item => convertBooleanFields(item, booleanFields));
  }

  // Handle objects
  if (typeof data === 'object') {
    const converted: any = { ...data };

    // First, convert boolean fields at this level
    booleanFields.forEach(field => {
      if (Object.prototype.hasOwnProperty.call(converted, field)) {
        // Convert 0/1 to false/true, handle null/undefined
        if (converted[field] === 0 || converted[field] === '0') {
          converted[field] = false;
        } else if (converted[field] === 1 || converted[field] === '1') {
          converted[field] = true;
        } else if (converted[field] === null || converted[field] === undefined) {
          converted[field] = false; // Default to false for null/undefined boolean fields
        }
        // If it's already a boolean, leave it as is
      }
    });

    // Then, recursively process nested objects and arrays (but skip Date objects)
    Object.keys(converted).forEach(key => {
      if (converted[key] && (typeof converted[key] === 'object' || Array.isArray(converted[key]))) {
        // Skip Date objects to prevent corruption
        if (converted[key] instanceof Date) {
          return; // Don't process Date objects
        }
        converted[key] = convertBooleanFields(converted[key], booleanFields);
      }
    });

    return converted;
  }

  return data;
};

/**
 * List of boolean fields in recipe data that need conversion
 */
export const RECIPE_BOOLEAN_FIELDS = [
  'has_recipe_public_visibility',
  'has_recipe_private_visibility',
  'is_ingredient_cooking_method',
  'is_preparation_method',
  'is_cost_manual',
  'is_bookmarked',
  'hasRecentChanges',
  'use_default',
  'may_contain'
];

/**
 * List of boolean fields in category data that need conversion
 */
export const CATEGORY_BOOLEAN_FIELDS = [
  'is_system_category',
  'hasIcon'
];

/**
 * Common boolean fields that might appear in various API responses
 */
export const COMMON_BOOLEAN_FIELDS = [
  ...RECIPE_BOOLEAN_FIELDS,
  ...CATEGORY_BOOLEAN_FIELDS
];

// Helper function to get the proper base URL
const getBaseUrl = (): string => {
  const baseUrl = global.config?.API_BASE_URL;

  if (
    baseUrl &&
    baseUrl.includes("/backend-api/v1/public/user/get-file?location=")
  ) {
    // API_BASE_URL already contains the full endpoint, return base part
    return baseUrl.replace(
      "/backend-api/v1/public/user/get-file?location=",
      ""
    );
  } else {
    // For development or when API_BASE_URL is just the base domain
    return (
      process.env.BASE_URL ||
      process.env.FRONTEND_URL ||
      "https://staging.namastevillage.theeasyaccess.com"
    );
  }
};

/**
 * Helper function to add freshness indicators to recipe data
 * @param recipe - Recipe data object
 * @param organizationId - Organization ID for filtering
 * @param options - Options to control which freshness checks to perform
 */
export const addFreshnessIndicators = async (
  recipe: any,
  organizationId: string,
  options: {
    checkCosts?: boolean;
    checkNutrition?: boolean;
    checkAll?: boolean;
  } = { checkAll: true }
): Promise<any> => {
  if (!recipe || !organizationId) return recipe;

  const recipeData = recipe.toJSON ? recipe.toJSON() : recipe;

  // Determine what to check based on options
  const shouldCheckCosts = options.checkAll || options.checkCosts;
  const shouldCheckNutrition = options.checkAll || options.checkNutrition;

  try {
    // Initialize freshness object
    recipeData.freshness = {
      costs: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.ingredient_costs_updated_at,
        checked: shouldCheckCosts,
      },
      nutrition: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.nutrition_values_updated_at,
        checked: shouldCheckNutrition,
      },
      overallStatus: {
        needsUpdate: false,
        lastChecked: new Date().toISOString(),
      },
    };

    // Perform checks only for requested fields
    const checks = [];

    if (shouldCheckCosts) {
      checks.push(
        areRecipeCostsOutdated(recipeData.id, organizationId).then(
          (result) => ({
            type: "costs",
            result,
          })
        )
      );
    }

    if (shouldCheckNutrition) {
      checks.push(
        areRecipeNutritionValuesOutdated(recipeData.id, organizationId).then(
          (result) => ({
            type: "nutrition",
            result,
          })
        )
      );
    }

    // Execute only the needed checks
    if (checks.length > 0) {
      const results = await Promise.all(checks);

      results.forEach(({ type, result }) => {
        if (type === "costs") {
          recipeData.freshness.costs.isOutdated = result.isOutdated;
          recipeData.freshness.costs.outdatedIngredients =
            result.outdatedIngredients;
        } else if (type === "nutrition") {
          recipeData.freshness.nutrition.isOutdated = result.isOutdated;
          recipeData.freshness.nutrition.outdatedIngredients =
            result.outdatedIngredients;
        }
      });

      // Update overall status
      recipeData.freshness.overallStatus.needsUpdate =
        recipeData.freshness.costs.isOutdated ||
        recipeData.freshness.nutrition.isOutdated;
    }
  } catch (error) {
    console.error("Error checking recipe freshness:", error);
    // Add default freshness indicators if check fails
    recipeData.freshness = {
      costs: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.ingredient_costs_updated_at,
        checked: shouldCheckCosts,
        error: true,
      },
      nutrition: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.nutrition_values_updated_at,
        checked: shouldCheckNutrition,
        error: true,
      },
      overallStatus: {
        needsUpdate: false,
        lastChecked: new Date().toISOString(),
        error: true,
      },
    };
  }

  return recipeData;
};

/**
 * Add freshness indicators specifically for cost-related updates
 */
export const addCostFreshnessIndicators = async (
  recipe: any,
  organizationId: string
): Promise<any> => {
  return addFreshnessIndicators(recipe, organizationId, {
    checkCosts: true,
    checkNutrition: false,
  });
};

/**
 * Add freshness indicators specifically for nutrition-related updates
 */
export const addNutritionFreshnessIndicators = async (
  recipe: any,
  organizationId: string
): Promise<any> => {
  return addFreshnessIndicators(recipe, organizationId, {
    checkCosts: false,
    checkNutrition: true,
  });
};

/**
 * Add freshness indicators for both costs and nutrition (default behavior)
 */
export const addAllFreshnessIndicators = async (
  recipe: any,
  organizationId: string
): Promise<any> => {
  return addFreshnessIndicators(recipe, organizationId, { checkAll: true });
};

/**
 * Determine what freshness checks to perform based on update context
 */
export const getContextualFreshnessIndicators = async (
  recipe: any,
  organizationId: string,
  updateContext: {
    updatedIngredients?: boolean;
    updatedCosts?: boolean;
    updatedNutrition?: boolean;
    updatedBasicInfo?: boolean;
  }
): Promise<any> => {
  // If only basic info (title, description, etc.) was updated, no freshness check needed
  if (
    updateContext.updatedBasicInfo &&
    !updateContext.updatedIngredients &&
    !updateContext.updatedCosts &&
    !updateContext.updatedNutrition
  ) {
    const recipeData = recipe.toJSON ? recipe.toJSON() : recipe;
    recipeData.freshness = {
      costs: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.ingredient_costs_updated_at,
        checked: false,
        reason: "No cost-related changes made",
      },
      nutrition: {
        isOutdated: false,
        outdatedIngredients: [],
        lastUpdated: recipeData.nutrition_values_updated_at,
        checked: false,
        reason: "No nutrition-related changes made",
      },
      overallStatus: {
        needsUpdate: false,
        lastChecked: new Date().toISOString(),
        reason: "Only basic info updated - no freshness check needed",
      },
    };
    return recipeData;
  }

  // Determine what to check based on what was updated
  const checkCosts =
    updateContext.updatedIngredients || updateContext.updatedCosts;
  const checkNutrition =
    updateContext.updatedIngredients || updateContext.updatedNutrition;

  return addFreshnessIndicators(recipe, organizationId, {
    checkCosts,
    checkNutrition,
  });
};

// Helper function to add file URLs to recipe data
export const addFileUrlsToRecipe = (recipe: any): any => {
  if (!recipe) return recipe;

  const baseUrl = getBaseUrl();
  const recipeData = recipe.toJSON ? recipe.toJSON() : recipe;

  // Add placeholder URL
  if (recipeData.placeholderItem?.item_location) {
    recipeData.placeholderItem.placeholderUrl = `${baseUrl}/backend-api/v1/public/user/get-file?location=${recipeData.placeholderItem.item_location}`;
    recipeData.placeholderItem.hasPlaceholder = true;
  } else if (recipeData.placeholderItem) {
    recipeData.placeholderItem.placeholderUrl = null;
    recipeData.placeholderItem.hasPlaceholder = false;
  }

  // Add step image URLs
  if (recipeData.steps && Array.isArray(recipeData.steps)) {
    recipeData.steps = recipeData.steps.map((step: any) => {
      if (step.stepItem?.item_location) {
        step.stepItem.stepImageUrl = `${baseUrl}/backend-api/v1/public/user/get-file?location=${step.stepItem.item_location}`;
        step.stepItem.hasStepImage = true;
      } else if (step.stepItem) {
        step.stepItem.stepImageUrl = null;
        step.stepItem.hasStepImage = false;
      }
      return step;
    });
  }

  // Add resource file URLs
  if (recipeData.resources && Array.isArray(recipeData.resources)) {
    recipeData.resources = recipeData.resources.map((resource: any) => {
      if (resource.type === "item" && resource.resourceItem?.item_location) {
        resource.resourceItem.resourceUrl = `${baseUrl}/backend-api/v1/public/user/get-file?location=${resource.resourceItem.item_location}`;
        resource.resourceItem.hasResourceFile = true;
      } else if (resource.resourceItem) {
        resource.resourceItem.resourceUrl = null;
        resource.resourceItem.hasResourceFile = false;
      }

      // For link resources, the URL is already in item_link field
      if (resource.type === "link" && resource.item_link) {
        resource.hasResourceLink = true;
      } else if (resource.type === "link") {
        resource.hasResourceLink = false;
      }

      return resource;
    });
  }

  // Add category icon URLs
  if (recipeData.categories && Array.isArray(recipeData.categories)) {
    recipeData.categories = recipeData.categories.map((category: any) => {
      if (category.iconItem?.item_location) {
        category.iconItem.iconUrl = `${baseUrl}/backend-api/v1/public/user/get-file?location=${category.iconItem.item_location}`;
        category.iconItem.hasIcon = true;
      } else if (category.iconItem) {
        category.iconItem.iconUrl = null;
        category.iconItem.hasIcon = false;
      }
      return category;
    });
  }

  // Organize attributes by type (nutrition, allergen, cuisine, dietary, haccp)
  if (recipeData.attributes && Array.isArray(recipeData.attributes)) {
    const attributesByType: any = {
      nutrition_attributes: [],
      allergen_attributes: { contains: [], may_contain: [] },
      cuisine_attributes: [],
      dietary_attributes: [],
      haccp_attributes: [],
    };

    recipeData.attributes.forEach((attr: any) => {
      const attributeData: any = {
        id: attr.id,
        attribute_title: attr.attribute_title,
        attribute_type: attr.attribute_type,
        unit: attr.RecipeAttributes?.unit || null,
        unit_of_measure: attr.RecipeAttributes?.unit_of_measure || null,
        attribute_description:
          attr.RecipeAttributes?.attribute_description || null,
        may_contain: attr.RecipeAttributes?.may_contain || false,
        use_default: attr.RecipeAttributes?.use_default || false,
      };

      // Add icon URL if available
      if (attr.iconItem?.item_location) {
        attributeData.iconUrl = `${baseUrl}/backend-api/v1/public/user/get-file?location=${attr.iconItem.item_location}`;
        attributeData.hasIcon = true;
      } else {
        attributeData.iconUrl = null;
        attributeData.hasIcon = false;
      }

      switch (attr.attribute_type) {
        case "nutrition":
          attributesByType.nutrition_attributes.push(attributeData);
          break;
        case "allergen":
          if (attributeData.may_contain) {
            attributesByType.allergen_attributes.may_contain.push(
              attributeData
            );
          } else {
            attributesByType.allergen_attributes.contains.push(attributeData);
          }
          break;
        case "cuisine":
          attributesByType.cuisine_attributes.push(attributeData);
          break;
        case "dietary":
          attributesByType.dietary_attributes.push(attributeData);
          break;
        case "haccp_category":
          attributesByType.haccp_attributes.push(attributeData);
          break;
        default:
          // Keep unknown types in original attributes array
          break;
      }
    });

    // Replace the original attributes with organized structure
    recipeData.nutrition_attributes = attributesByType.nutrition_attributes;
    recipeData.allergen_attributes = attributesByType.allergen_attributes;
    recipeData.cuisine_attributes = attributesByType.cuisine_attributes;
    recipeData.dietary_attributes = attributesByType.dietary_attributes;
    recipeData.haccp_attributes = attributesByType.haccp_attributes;
  }

  return recipeData;
};

// Helper function to add file URLs to multiple recipes
export const addFileUrlsToRecipes = (recipes: any[]): any[] => {
  if (!recipes || !Array.isArray(recipes)) return recipes;

  return recipes.map((recipe) => addFileUrlsToRecipe(recipe));
};

// Interface for history tracking
interface HistoryData {
  recipe_id: number;
  action: RecipeHistoryAction;
  field_name?: string;
  old_value?: string;
  new_value?: string;
  description?: string;
  ip_address?: string;
  user_agent?: string;
  organization_id?: string;
  created_by: number;
  created_at?: Date;
}

/**
 * Create history record for recipe changes
 */
export const createRecipeHistory = async (
  historyData: HistoryData,
  transaction?: any
): Promise<void> => {
  try {
    // Set created_at if not provided
    if (!historyData.created_at) {
      historyData.created_at = new Date();
    }

    await RecipeHistory.create(historyData, { transaction });
  } catch (error) {
    console.error("Error creating recipe history:", error);
    // Don't throw error to prevent breaking main operation
  }
};

/**
 * Get common recipe query options with all relations
 */
export const getRecipeQueryOptions = (includeInactive: boolean = false) => {
  const statusCondition = includeInactive
    ? {}
    : { status: { [Op.ne]: "inactive" } };

  const ingredientStatusCondition = includeInactive
    ? {}
    : { recipe_ingredient_status: { [Op.ne]: "inactive" } };

  return {
    include: [
      {
        model: Category,
        as: "categories",
        through: {
          attributes: ["status"],
          where: statusCondition,
        },
        attributes: ["id", "category_name", "category_slug", "category_status"],
        include: [
          {
            model: Item,
            as: "iconItem",
            attributes: ["id", "item_name", "item_location", "item_mime_type"],
            required: false,
          },
        ],
        required: false,
      },
      {
        model: FoodAttributes,
        as: "attributes",
        through: {
          attributes: [
            "unit_of_measure",
            "unit",
            "status",
            "attribute_description",
            "may_contain",
            "use_default",
          ],
          where: statusCondition,
        },
        attributes: [
          "id",
          "attribute_title",
          "attribute_slug",
          "attribute_type",
        ],
        include: [
          {
            model: Item,
            as: "iconItem",
            attributes: ["id", "item_name", "item_location", "item_mime_type"],
            required: false,
          },
        ],
        required: false,
      },
      {
        model: Ingredient,
        as: "ingredients",
        through: {
          attributes: [
            "recipe_ingredient_status",
            "ingredient_quantity",
            "ingredient_measure",
            "ingredient_wastage",
            "ingredient_cost",
            "ingredient_cooking_method",
            "preparation_method",
          ],
          where: ingredientStatusCondition,
        },
        attributes: [
          "id",
          "ingredient_name",
          "ingredient_description",
          "ingredient_status",
        ],
        required: false,
      },
      {
        model: RecipeSteps,
        as: "steps",
        where: statusCondition,
        attributes: [
          "id",
          "recipe_step_order",
          "recipe_step_description",
          "item_id",
          "status",
        ],
        include: [
          {
            model: Item,
            as: "stepItem",
            attributes: ["id", "item_name", "item_location", "item_mime_type"],
            required: false,
          },
        ],
        required: false,
        order: [["recipe_step_order", "ASC"]],
      },
      {
        model: RecipeResources,
        as: "resources",
        where: statusCondition,
        attributes: [
          "id",
          "type",
          "item_id",
          "item_link",
          "item_link_type",
          "status",
        ],
        include: [
          {
            model: Item,
            as: "resourceItem",
            attributes: ["id", "item_name", "item_location", "item_mime_type"],
            required: false,
          },
        ],
        required: false,
      },
      {
        model: Item,
        as: "placeholderItem",
        attributes: ["id", "item_name", "item_location", "item_mime_type"],
        required: false,
      },
      {
        model: User,
        as: "creator",
        attributes: ["id", "user_email"],
        required: false,
      },
      {
        model: User,
        as: "updater",
        attributes: ["id", "user_email"],
        required: false,
      },
    ],
  };
};

/**
 * Build where conditions for recipe filtering
 */
export const buildRecipeWhereConditions = (
  filters: any,
  isPublic: boolean = false
) => {
  const whereConditions: any = {};

  // Organization filter
  if (filters.organization_id) {
    whereConditions.organization_id = filters.organization_id;
  }

  // Status filter
  if (filters.recipe_status) {
    whereConditions.recipe_status = filters.recipe_status;
  } else if (isPublic) {
    // For public recipes, only show published ones
    whereConditions.recipe_status = "publish";
    whereConditions.has_recipe_public_visibility = true;
  } else {
    // For private recipes, exclude deleted ones by default
    whereConditions.recipe_status = { [Op.ne]: "deleted" };
  }

  // Search filter (MySQL compatible)
  if (filters.search) {
    whereConditions[Op.or] = [
      { recipe_title: { [Op.like]: `%${filters.search}%` } },
      { recipe_public_title: { [Op.like]: `%${filters.search}%` } },
      { recipe_serve_in: { [Op.like]: `%${filters.search}%` } },
      { recipe_garnish: { [Op.like]: `%${filters.search}%` } },
      { recipe_head_chef_tips: { [Op.like]: `%${filters.search}%` } },
      { recipe_foh_tips: { [Op.like]: `%${filters.search}%` } },
    ];
  }

  return whereConditions;
};

/**
 * Build include conditions for recipe filtering by relations
 */
export const buildRecipeIncludeConditions = (filters: any) => {
  const includeConditions: any[] = [];

  // Category filter
  if (filters.category) {
    const categoryIds = filters.category
      .split(",")
      .map((id: string) => parseInt(id.trim()));
    includeConditions.push({
      model: Category,
      as: "categories",
      where: { id: { [Op.in]: categoryIds } },
      through: { where: { status: "active" } },
      required: true,
    });
  }

  // Attribute filter
  if (filters.attribute) {
    const attributeIds = filters.attribute
      .split(",")
      .map((id: string) => parseInt(id.trim()));
    includeConditions.push({
      model: FoodAttributes,
      as: "attributes",
      where: { id: { [Op.in]: attributeIds } },
      through: { where: { status: "active" } },
      required: true,
    });
  }

  // Ingredient filter
  if (filters.ingredient) {
    const ingredientIds = filters.ingredient
      .split(",")
      .map((id: string) => parseInt(id.trim()));
    includeConditions.push({
      model: Ingredient,
      as: "ingredients",
      where: { id: { [Op.in]: ingredientIds } },
      through: { where: { recipe_ingredient_status: "active" } },
      required: true,
    });
  }

  return includeConditions;
};

/**
 * Export recipes to Excel format
 */
export const exportRecipesToExcel = async (
  recipes: any[],
  filters: any
): Promise<ExcelJS.Workbook> => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet("Recipes");

  // Add metadata header
  worksheet.addRow(["Recipe Export Report"]);
  worksheet.addRow(["Generated on:", new Date().toISOString()]);
  worksheet.addRow(["Filters Applied:", JSON.stringify(filters)]);
  worksheet.addRow([]); // Empty row

  // Define headers
  const headers = [
    "ID",
    "Title",
    "Public Title",
    "Status",
    "Prep Time (min)",
    "Cook Time (min)",
    "Public Visibility",
    "Private Visibility",
    "Serve In",
    "Complexity Level",
    "Garnish",
    "Chef Tips",
    "FOH Tips",
    "Impression Count",
    "Yield",
    "Total Portions",
    "Single Portion Size",
    "Serving Method",
    "Categories",
    "Ingredients Count",
    "Steps Count",
    "Resources Count",
    "Assigned Users",
    "Created By",
    "Created At",
    "Updated At",
  ];

  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true };
  headerRow.fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Add data rows
  recipes.forEach((recipe: any) => {
    const categories =
      recipe.categories?.map((cat: any) => cat.category_name).join(", ") || "";

    worksheet.addRow([
      recipe.id,
      recipe.recipe_title,
      recipe.recipe_public_title || "",
      recipe.recipe_status,
      recipe.recipe_preparation_time || 0,
      recipe.recipe_cook_time || 0,
      recipe.has_recipe_public_visibility ? "Yes" : "No",
      recipe.has_recipe_private_visibility ? "Yes" : "No",
      recipe.recipe_serve_in || "",
      recipe.recipe_complexity_level || "",
      recipe.recipe_garnish || "",
      recipe.recipe_head_chef_tips || "",
      recipe.recipe_foh_tips || "",
      recipe.recipe_impression || 0,
      recipe.recipe_yield || 0,
      recipe.recipe_yield_unit || "",
      recipe.recipe_total_portions || 0,
      recipe.recipe_single_portion_size || 0,
      recipe.recipe_serving_method || "",
      categories,
      recipe.ingredients?.length || 0,
      recipe.steps?.length || 0,
      recipe.resources?.length || 0,
      recipe.assigned_users
        ?.map((user: any) => user.user_full_name)
        .join(", ") || "",
      recipe.creator?.user_email || "",
      recipe.created_at,
      recipe.updated_at,
    ]);
  });

  // Auto-fit columns
  worksheet.columns.forEach((column: any) => {
    column.width = Math.max(column.width || 10, 15);
  });

  return workbook;
};

/**
 * Export recipes to CSV format
 */
export const exportRecipesToCSV = (recipes: any[]): string => {
  const headers = [
    "ID",
    "Title",
    "Public Title",
    "Status",
    "Prep Time (min)",
    "Cook Time (min)",
    "Public Visibility",
    "Private Visibility",
    "Complexity Level",
    "Yield",
    "Total Portions",
    "Single Portion Size",
    "Serving Method",
    "Categories",
    "Ingredients Count",
    "Steps Count",
    "Resources Count",
    "Assigned Users",
    "Created By",
    "Created At",
  ];

  const csvRows = [headers.join(",")];

  recipes.forEach((recipe: any) => {
    const categories =
      recipe.categories?.map((cat: any) => cat.category_name).join("; ") || "";

    const row = [
      recipe.id,
      `"${recipe.recipe_title}"`,
      `"${recipe.recipe_public_title || ""}"`,
      recipe.recipe_status,
      recipe.recipe_preparation_time || 0,
      recipe.recipe_cook_time || 0,
      recipe.has_recipe_public_visibility ? "Yes" : "No",
      recipe.has_recipe_private_visibility ? "Yes" : "No",
      `"${recipe.recipe_complexity_level || ""}"`,
      recipe.recipe_yield || 0,
      recipe.recipe_yield_unit || "",
      recipe.recipe_total_portions || 0,
      recipe.recipe_single_portion_size || 0,
      `"${recipe.recipe_serving_method || ""}"`,
      `"${categories}"`,
      recipe.ingredients?.length || 0,
      recipe.steps?.length || 0,
      recipe.resources?.length || 0,
      `"${recipe.assigned_users?.map((user: any) => user.user_full_name).join(", ") || ""}"`,
      `"${recipe.creator?.user_email || ""}"`,
      recipe.created_at,
    ];

    csvRows.push(row.join(","));
  });

  return csvRows.join("\n");
};

/**
 * Helper function to replace placeholders in HTML template with actual data
 */
const replacePlaceholders = (template: string, data: any): string => {
  let html = template;

  // Check if this is the new template design
  const isNewTemplate = html.includes("{{ALLERGEN_ICONS}}");

  if (isNewTemplate) {
    return replaceNewTemplatePlaceholders(template, data);
  }

  // Basic recipe information
  html = html.replace(
    /{{RECIPE_TITLE}}/g,
    data.recipe_title || "Untitled Recipe"
  );
  html = html.replace(/{{RECIPE_SUBTITLE}}/g, data.recipe_public_title || "");
  html = html.replace(
    /{{RECIPE_SUBTITLE_HIDDEN}}/g,
    data.recipe_public_title ? "" : "hidden"
  );
  html = html.replace(/{{RECIPE_DESCRIPTION}}/g, data.recipe_description || "");
  html = html.replace(
    /{{RECIPE_DESCRIPTION_HIDDEN}}/g,
    data.recipe_description ? "" : "hidden"
  );
  html = html.replace(/{{RECIPE_ID}}/g, data.id || "-");
  html = html.replace(/{{RECIPE_STATUS}}/g, data.recipe_status || "-");
  html = html.replace(
    /{{RECIPE_PREP_TIME}}/g,
    data.recipe_preparation_time ? `${data.recipe_preparation_time} min` : "-"
  );
  html = html.replace(
    /{{RECIPE_COOK_TIME}}/g,
    data.recipe_cook_time ? `${data.recipe_cook_time} min` : "-"
  );

  // Calculate total time
  const prepTime = parseInt(data.recipe_preparation_time) || 0;
  const cookTime = parseInt(data.recipe_cook_time) || 0;
  const totalTime = prepTime + cookTime;
  html = html.replace(
    /{{RECIPE_TOTAL_TIME}}/g,
    totalTime > 0 ? `${totalTime} min` : "-"
  );

  html = html.replace(
    /{{RECIPE_COMPLEXITY}}/g,
    data.recipe_complexity_level || "-"
  );
  html = html.replace(/{{RECIPE_YIELD}}/g, data.recipe_yield || "-");
  html = html.replace(
    /{{RECIPE_PORTIONS}}/g,
    data.recipe_total_portions || "-"
  );
  html = html.replace(
    /{{RECIPE_SERVING_SIZE}}/g,
    data.recipe_single_portion_size || "-"
  );
  html = html.replace(
    /{{GENERATION_DATE}}/g,
    data.generation_date || new Date().toLocaleDateString()
  );

  // Organization information
  html = html.replace(
    /{{ORGANIZATION_NAME}}/g,
    data.organization_name || "Recipe Management System"
  );
  html = html.replace(/{{ORGANIZATION_LOGO}}/g, data.organization_logo || "");
  html = html.replace(
    /{{ORG_LOGO_HIDDEN}}/g,
    data.organization_logo ? "" : "hidden"
  );
  html = html.replace(
    /{{ORG_LOGO_VISIBLE}}/g,
    data.organization_logo ? "hidden" : ""
  );

  // Creator and updater information
  html = html.replace(
    /{{CREATOR_NAME}}/g,
    data.creator_user_full_name || "Unknown"
  );
  html = html.replace(
    /{{UPDATER_NAME}}/g,
    data.updater_user_full_name || "Unknown"
  );

  // Recipe image
  html = html.replace(
    /{{RECIPE_IMAGE_URL}}/g,
    data.item_detail?.item_link || ""
  );
  html = html.replace(
    /{{RECIPE_IMAGE_HIDDEN}}/g,
    data.item_detail?.item_link ? "" : "hidden"
  );

  // Categories
  const categoriesHtml =
    data.categories && data.categories.length > 0
      ? data.categories
        .map(
          (cat: any) =>
            `<div class="category-badge">${cat.category_name || "Unknown Category"}</div>`
        )
        .join("")
      : "";
  html = html.replace(/{{CATEGORIES_LIST}}/g, categoriesHtml);
  html = html.replace(/{{CATEGORIES_HIDDEN}}/g, categoriesHtml ? "" : "hidden");

  // Ingredients with totals calculation
  let ingredientsHtml = "";
  let totalQuantity = 0;
  let totalCost = 0;
  let ingredientsTotalsHtml = "";

  if (data.ingredients && data.ingredients.length > 0) {
    // Generate ingredient rows and calculate totals
    ingredientsHtml = data.ingredients
      .map((ing: any) => {
        const quantity = parseFloat(ing.ingredient_quantity) || 0;
        const cost = parseFloat(ing.ingredient_cost) || 0;

        // Add to totals (only if values are valid numbers)
        if (!isNaN(quantity) && quantity > 0) {
          totalQuantity += quantity;
        }
        if (!isNaN(cost) && cost > 0) {
          totalCost += cost;
        }

        // Build preparation details
        const preparationDetails = [];
        if (ing.ingredient_cooking_method) {
          preparationDetails.push(ing.ingredient_cooking_method);
        }
        if (ing.preparation_method) {
          preparationDetails.push(ing.preparation_method);
        }
        const preparationText =
          preparationDetails.length > 0 ? preparationDetails.join(", ") : "-";

        return `
        <tr>
          <td class="ingredient-name"><strong>${ing.ingredient_name || "-"}</strong></td>
          <td class="ingredient-quantity">${ing.ingredient_quantity || "-"}</td>
          <td class="ingredient-unit">${ing.measure_title || "-"}</td>
          <td class="ingredient-preparation">${preparationText}</td>
          <td class="ingredient-cost">${ing.ingredient_cost ? `$${parseFloat(ing.ingredient_cost).toFixed(2)}` : "-"}</td>
        </tr>
      `;
      })
      .join("");

    // Generate totals row with better formatting
    const totalQuantityDisplay =
      totalQuantity > 0
        ? totalQuantity % 1 === 0
          ? totalQuantity.toString()
          : totalQuantity.toFixed(2)
        : "-";

    const totalCostDisplay = totalCost > 0 ? `$${totalCost.toFixed(2)}` : "-";

    ingredientsTotalsHtml = `
      <tr class="total-row">
        <td class="ingredient-name"><strong>📊 TOTAL SUMMARY</strong></td>
        <td class="ingredient-quantity"><strong>${totalQuantityDisplay}</strong></td>
        <td class="ingredient-unit"><strong>${data.ingredients.length} Items</strong></td>
        <td class="ingredient-preparation"><strong>-</strong></td>
        <td class="ingredient-cost"><strong>${totalCostDisplay}</strong></td>
      </tr>
    `;
  }

  html = html.replace(/{{INGREDIENTS_LIST}}/g, ingredientsHtml);
  html = html.replace(/{{INGREDIENTS_TOTALS}}/g, ingredientsTotalsHtml);
  html = html.replace(
    /{{INGREDIENTS_HIDDEN}}/g,
    ingredientsHtml ? "" : "hidden"
  );

  // Steps
  const stepsHtml =
    data.steps && data.steps.length > 0
      ? data.steps
        .map((step: any, index: number) => {
          const stepNumber = step.recipe_step_order || index + 1;
          const stepImageUrl = step.item_detail?.item_link || step.item_id;
          const stepImage = stepImageUrl
            ? `
          <div class="step-image">
            <img src="${stepImageUrl}" alt="Step ${stepNumber} image"
                 onerror="this.style.display='none';"
                 onload="this.style.display='block';">
          </div>
        `
            : "";
          return `
          <div class="step">
            <div class="step-number">${stepNumber}</div>
            <div class="step-content">
              <div class="step-title">Step ${stepNumber}</div>
              <div class="step-description">${step.recipe_step_description || "No description available"}</div>
              ${stepImage}
            </div>
          </div>
        `;
        })
        .join("")
      : "";
  html = html.replace(/{{STEPS_LIST}}/g, stepsHtml);
  html = html.replace(/{{STEPS_HIDDEN}}/g, stepsHtml ? "" : "hidden");

  // Tips
  const tipsArray = [];
  if (data.recipe_serve_in) {
    tipsArray.push(`
      <div class="tip-card">
        <div class="tip-header">
          <span class="tip-icon">🍽️</span>
          <span class="tip-title">Serving Instructions</span>
        </div>
        <div class="tip-content">${data.recipe_serve_in}</div>
      </div>
    `);
  }
  if (data.recipe_garnish) {
    tipsArray.push(`
      <div class="tip-card">
        <div class="tip-header">
          <span class="tip-icon">🌿</span>
          <span class="tip-title">Garnish</span>
        </div>
        <div class="tip-content">${data.recipe_garnish}</div>
      </div>
    `);
  }
  if (data.recipe_head_chef_tips) {
    tipsArray.push(`
      <div class="tip-card">
        <div class="tip-header">
          <span class="tip-icon">👨‍🍳</span>
          <span class="tip-title">Chef Tips</span>
        </div>
        <div class="tip-content">${data.recipe_head_chef_tips}</div>
      </div>
    `);
  }
  if (data.recipe_foh_tips) {
    tipsArray.push(`
      <div class="tip-card">
        <div class="tip-header">
          <span class="tip-icon">🏨</span>
          <span class="tip-title">FOH Tips</span>
        </div>
        <div class="tip-content">${data.recipe_foh_tips}</div>
      </div>
    `);
  }
  const tipsHtml = tipsArray.join("");
  html = html.replace(/{{TIPS_LIST}}/g, tipsHtml);
  html = html.replace(/{{TIPS_HIDDEN}}/g, tipsHtml ? "" : "hidden");

  // Nutrition content
  let nutritionContent = "";
  if (
    data.nutrition_attributes ||
    data.allergen_attributes ||
    data.dietary_attributes ||
    data.cuisine_attributes
  ) {
    // Add nutrition info
    if (data.nutrition_attributes && data.nutrition_attributes.length > 0) {
      const nutritionItems = data.nutrition_attributes
        .map(
          (attr: any) =>
            `<p><strong>${attr.attribute_title || "-"}:</strong> ${attr.unit || "-"} ${attr.unit_of_measure || ""}</p>`
        )
        .join("");
      nutritionContent += `
        <div class="tip-card mb-20">
          <div class="tip-header">
            <span class="tip-icon">📊</span>
            <span class="tip-title">Nutritional Information</span>
          </div>
          <div class="tip-content">${nutritionItems}</div>
        </div>
      `;
    }

    // Add allergen info
    if (data.allergen_attributes) {
      const containsItems = data.allergen_attributes.contains
        ? data.allergen_attributes.contains
          .map((attr: any) => `<li>${attr.attribute_title || "-"}</li>`)
          .join("")
        : "";
      const mayContainItems = data.allergen_attributes.may_contain
        ? data.allergen_attributes.may_contain
          .map((attr: any) => `<li>${attr.attribute_title || "-"}</li>`)
          .join("")
        : "";

      nutritionContent += `
        <div class="tip-card mb-20" style="border-top-color: #e74c3c;">
          <div class="tip-header">
            <span class="tip-icon">⚠️</span>
            <span class="tip-title">Allergen Information</span>
          </div>
          <div class="tip-content">
            <p><strong style="color: #c0392b;">Contains:</strong></p>
            ${containsItems ? `<ul>${containsItems}</ul>` : "<p>-</p>"}
            <p><strong style="color: #f39c12;">May Contain:</strong></p>
            ${mayContainItems ? `<ul>${mayContainItems}</ul>` : "<p>-</p>"}
          </div>
        </div>
      `;
    }

    // Add dietary and cuisine badges
    const dietaryBadges = data.dietary_attributes
      ? data.dietary_attributes
        .map(
          (attr: any) =>
            `<div class="category-badge" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);">🌱 ${attr.attribute_title}</div>`
        )
        .join("")
      : "";

    const cuisineBadges = data.cuisine_attributes
      ? data.cuisine_attributes
        .map(
          (attr: any) =>
            `<div class="category-badge" style="background: linear-gradient(135deg, #135e96 0%, #0f4a7a 100%);">🍽️ ${attr.attribute_title}</div>`
        )
        .join("")
      : "";

    if (dietaryBadges || cuisineBadges) {
      nutritionContent += `<div class="categories-grid">${dietaryBadges}${cuisineBadges}</div>`;
    }
  }
  html = html.replace(/{{NUTRITION_CONTENT}}/g, nutritionContent);
  html = html.replace(
    /{{NUTRITION_HIDDEN}}/g,
    nutritionContent ? "" : "hidden"
  );

  // HACCP content
  let haccpContent = "";
  if (data.haccp_attributes && data.haccp_attributes.length > 0) {
    haccpContent = data.haccp_attributes
      .map(
        (haccp: any) => `
      <div class="haccp-item">
        <div class="haccp-item-header">
          <span class="haccp-item-icon">🛡️</span>
          <span class="haccp-item-title">${haccp.attribute_title || "HACCP Point"}</span>
        </div>
        <div class="haccp-item-description">${haccp.attribute_description || "No description available"}</div>
      </div>
    `
      )
      .join("");
  }
  html = html.replace(/{{HACCP_CONTENT}}/g, haccpContent);
  html = html.replace(/{{HACCP_HIDDEN}}/g, haccpContent ? "" : "hidden");

  // Resources
  const resourcesHtml =
    data.resources && data.resources.length > 0
      ? data.resources
        .map((resource: any) => {
          const resourceIcon = resource.resource_type?.includes("image")
            ? "🖼️"
            : resource.resource_type?.includes("video")
              ? "🎥"
              : resource.resource_type?.includes("pdf")
                ? "📄"
                : resource.resource_type?.includes("doc")
                  ? "📝"
                  : "📎";

          let resourceContent = "";
          if (
            resource.resource_type?.includes("image") &&
            resource.resource_link
          ) {
            resourceContent = `
          <img src="${resource.resource_link}" alt="${resource.resource_name}" class="resource-image"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
          <div class="image-fallback" style="display: none;">
            🖼️ Image not available
          </div>
        `;
          } else if (resource.resource_link) {
            resourceContent = `
          <div class="resource-link">
            <a href="${resource.resource_link}" target="_blank" rel="noopener noreferrer"
               style="color: #135e96 !important; text-decoration: underline !important; font-weight: 600 !important; display: inline-block; padding: 8px 16px; background: #f8f9fa; border-radius: 6px; border: 2px solid #135e96;">
              🔗 View Resource
            </a>
          </div>
        `;
          } else {
            resourceContent =
              '<div class="resource-link"><span style="color: #6c757d; font-size: 14px;">No link available</span></div>';
          }

          return `
          <div class="resource-card">
            <div class="resource-header">
              <span class="resource-icon">${resourceIcon}</span>
              <span class="resource-title">${resource.resource_name || "Resource"}</span>
            </div>
            ${resource.resource_description ? `<div class="resource-description">${resource.resource_description}</div>` : ""}
            ${resourceContent}
          </div>
        `;
        })
        .join("")
      : "";
  html = html.replace(/{{RESOURCES_LIST}}/g, resourcesHtml);
  html = html.replace(/{{RESOURCES_HIDDEN}}/g, resourcesHtml ? "" : "hidden");

  return html;
};

/**
 * Helper function to replace placeholders for the new template design using real API data
 */
const replaceNewTemplatePlaceholders = (
  template: string,
  data: any
): string => {
  let html = template;

  // Basic recipe information from actual API data

  html = html.replace(
    /{{RECIPE_TITLE}}/g,
    data.recipe_title || "Untitled Recipe"
  );
  html = html.replace(/{{RECIPE_SUBTITLE}}/g, data.recipe_public_title || "");
  html = html.replace(
    /{{RECIPE_SUBTITLE_HIDDEN}}/g,
    data.recipe_public_title ? "" : "hidden"
  );
  html = html.replace(/{{RECIPE_DESCRIPTION}}/g, data.recipe_description || "");
  html = html.replace(
    /{{RECIPE_DESCRIPTION_HIDDEN}}/g,
    data.recipe_description ? "" : "hidden"
  );

  // Recipe image from the actual data structure
  const imageUrl = data.item_detail?.item_link || "";
  html = html.replace(/{{RECIPE_IMAGE_URL}}/g, imageUrl);
  html = html.replace(/{{RECIPE_IMAGE_HIDDEN}}/g, imageUrl ? "" : "hidden");

  // Organization information

  html = html.replace(
    /{{ORGANIZATION_NAME}}/g,
    data.organization_name || "Recipe Management System"
  );
  html = html.replace(
    /{{CREATOR_NAME}}/g,
    data.creator_user_full_name || "Unknown"
  );
  html = html.replace(
    /{{GENERATION_DATE}}/g,
    data.generation_date || new Date().toLocaleDateString()
  );

  // Recipe timing information from actual API data
  const prepTime = parseInt(data.recipe_preparation_time) || 0;
  const cookTime = parseInt(data.recipe_cook_time) || 0;
  const totalTime = prepTime + cookTime;

  html = html.replace(
    /{{RECIPE_PREP_TIME}}/g,
    prepTime > 0 ? `${prepTime} min` : "-"
  );
  html = html.replace(
    /{{RECIPE_COOK_TIME}}/g,
    cookTime > 0 ? `${cookTime} min` : "-"
  );
  html = html.replace(
    /{{RECIPE_TOTAL_TIME}}/g,
    totalTime > 0 ? `${totalTime} min` : "-"
  );
  html = html.replace(
    /{{RECIPE_SERVINGS}}/g,
    data.recipe_yield || data.recipe_total_portions || "-"
  );

  // Process allergen information from actual API data structure
  let allergenIcons = "";
  let allergenText = "";

  if (data.allergen_attributes) {
    const containsAllergens = data.allergen_attributes.contains || [];
    const mayContainAllergens = data.allergen_attributes.may_contain || [];

    // Create allergen icons based on actual allergen data from API
    const allAllergens = [...containsAllergens, ...mayContainAllergens];
    if (allAllergens.length > 0) {
      allergenIcons = allAllergens
        .map((allergen: any) => {
          // Use actual allergen icon from item_detail if available
          if (allergen.item_detail?.item_link) {
            return `<img src="${allergen.item_detail.item_link}" alt="${allergen.attribute_title}" class="allergen-icon">`;
          }
          // Map common allergens to emojis as fallback
          const iconMap: any = {
            milk: "🥛",
            dairy: "🥛",
            gluten: "🌾",
            wheat: "🌾",
            eggs: "🥚",
            egg: "�",
            nuts: "🥜",
            peanuts: "🥜",
            "tree nuts": "🥜",
            fish: "🐟",
            soy: "🫘",
            soya: "🫘",
            shellfish: "🦐",
            crustaceans: "🦐",
            molluscs: "🦪",
            sesame: "🌰",
            celery: "🥬",
            mustard: "🌭",
            sulphites: "🍷",
            lupin: "🌱",
          };
          const allergenName = allergen.attribute_title?.toLowerCase() || "";
          const icon = Object.keys(iconMap).find((key) =>
            allergenName.includes(key)
          );
          return icon ? iconMap[icon] : "⚠️";
        })
        .join(" ");

      // Build allergen text from actual data
      if (containsAllergens.length > 0) {
        allergenText = `Contains: ${containsAllergens.map((a: any) => a.attribute_title).join(", ")}`;
      }
      if (mayContainAllergens.length > 0) {
        allergenText += allergenText
          ? `. May contain: ${mayContainAllergens.map((a: any) => a.attribute_title).join(", ")}`
          : `May contain: ${mayContainAllergens.map((a: any) => a.attribute_title).join(", ")}`;
      }
    }
  }

  html = html.replace(
    /{{ALLERGEN_ICONS}}/g,
    allergenIcons || "No allergen information"
  );

  // Process categories and dietary information from actual API data
  let recipeTags = "";

  // Add categories from actual API data
  if (data.categories && data.categories.length > 0) {
    recipeTags = data.categories
      .map((cat: any) => cat.category_name)
      .join(", ");
  }

  // Add dietary preferences from actual API data
  if (data.dietary_attributes && data.dietary_attributes.length > 0) {
    const dietaryItems = data.dietary_attributes
      .map((diet: any) => diet.attribute_title)
      .join(", ");
    recipeTags = recipeTags ? `${recipeTags}, ${dietaryItems}` : dietaryItems;
  }

  // Add cuisine attributes from actual API data
  if (data.cuisine_attributes && data.cuisine_attributes.length > 0) {
    const cuisineItems = data.cuisine_attributes
      .map((cuisine: any) => cuisine.attribute_title)
      .join(", ");
    recipeTags = recipeTags ? `${recipeTags}, ${cuisineItems}` : cuisineItems;
  }

  // Add complexity level from actual API data
  if (data.recipe_complexity_level) {
    recipeTags = recipeTags
      ? `${recipeTags}, ${data.recipe_complexity_level} complexity`
      : `${data.recipe_complexity_level} complexity`;
  }

  // Add serving method from actual API data
  if (data.recipe_serving_method) {
    recipeTags = recipeTags
      ? `${recipeTags}, ${data.recipe_serving_method}`
      : data.recipe_serving_method;
  }

  html = html.replace(
    /{{RECIPE_TAGS}}/g,
    recipeTags || allergenText || "No specific information available"
  );

  // Calculate costs from actual ingredient data from API
  let totalCost = 0;

  if (data.ingredients && data.ingredients.length > 0) {
    totalCost = data.ingredients.reduce((sum: number, ing: any) => {
      const cost = parseFloat(ing.ingredient_cost) || 0;
      const quantity = parseFloat(ing.ingredient_quantity) || 0;
      const ingredientTotal = cost * quantity;
      return sum + ingredientTotal;
    }, 0);
  }

  // Use actual cost data from API or calculated values
  const batchCost = totalCost;
  const totalPortions =
    parseFloat(data.recipe_total_portions) ||
    parseFloat(data.recipe_yield) ||
    1;
  const portionCost =
    batchCost > 0 && totalPortions > 0 ? batchCost / totalPortions : 0;

  html = html.replace(
    /{{BATCH_COST}}/g,
    batchCost > 0 ? `£${batchCost.toFixed(2)}` : "£0.00"
  );
  html = html.replace(
    /{{SERVING_COST}}/g,
    portionCost > 0 ? `£${portionCost.toFixed(2)}` : "£0.00"
  );

  // Yield information from actual recipe API data
  const cookedWeight = data.recipe_yield
    ? `${data.recipe_yield}${data.recipe_yield_unit ? ` ${data.recipe_yield_unit}` : ""}`
    : "-";
  const servingSize = data.recipe_single_portion_size
    ? `${data.recipe_single_portion_size}g`
    : "-";
  const servingsPerBatch = data.recipe_total_portions || "-";

  html = html.replace(/{{COOKED_WEIGHT}}/g, cookedWeight);
  html = html.replace(/{{SERVING_SIZE}}/g, servingSize);
  html = html.replace(/{{SERVINGS_PER_BATCH}}/g, servingsPerBatch);

  // Process ingredients list with actual API data structure
  let ingredientsHtml = "";

  if (data.ingredients && data.ingredients.length > 0) {
    ingredientsHtml = data.ingredients
      .map((ing: any) => {
        // Build preparation details from available fields in API data
        const preparationDetails = [];

        // Check for cooking method from API data
        if (ing.ingredient_cooking_method_title) {
          preparationDetails.push(
            `Cooking: ${ing.ingredient_cooking_method_title}`
          );
        }

        // Check for preparation method from API data
        if (ing.preparation_method_title) {
          preparationDetails.push(`Prep: ${ing.preparation_method_title}`);
        }

        // Check for ingredient description from API data
        if (ing.ingredient_description) {
          preparationDetails.push(ing.ingredient_description);
        }

        // If no preparation details, use a basic description
        const preparationText =
          preparationDetails.length > 0
            ? preparationDetails.join(", ")
            : "Standard preparation";

        // Format quantity with unit from API data
        const quantity = ing.ingredient_quantity || "-";
        const unit = ing.measure_title || "";
        const quantityDisplay = unit ? `${quantity} ${unit}` : quantity;

        // Calculate total cost for this ingredient
        const unitCost = parseFloat(ing.ingredient_cost) || 0;
        const ingredientQuantity = parseFloat(ing.ingredient_quantity) || 0;
        const totalCost = unitCost * ingredientQuantity;

        return `
        <tr>
          <td><strong>${ing.ingredient_name || "Unknown Ingredient"}</strong></td>
          <td>${quantityDisplay}</td>
          <td>£${totalCost.toFixed(2)}</td>
          <td>${preparationText}</td>
        </tr>
      `;
      })
      .join("");
  } else {
    ingredientsHtml =
      '<tr><td colspan="4" style="text-align: center; font-style: italic;">No ingredients specified</td></tr>';
  }
  html = html.replace(/{{INGREDIENTS_LIST}}/g, ingredientsHtml);

  // Process method content from actual API steps data
  let methodContent = "";

  if (data.steps && data.steps.length > 0) {
    methodContent = data.steps
      .sort(
        (a: any, b: any) =>
          (a.recipe_step_order || 0) - (b.recipe_step_order || 0)
      )
      .map((step: any, index: number) => {
        const stepNumber = step.recipe_step_order || index + 1;
        const stepDescription =
          step.recipe_step_description || "No description provided";
        const stepImage = step.item_detail?.item_link;

        let stepHtml = `<div class="method-step">
          <div class="step-header">
            <strong>Step ${stepNumber}:</strong>
          </div>
          <div class="step-content">
            <div class="step-description">${stepDescription}</div>`;

        if (stepImage) {
          stepHtml += `
            <div class="step-image-container">
              <img src="${stepImage}" alt="Step ${stepNumber}" class="step-image" onerror="this.style.display='none'">
            </div>`;
        }

        stepHtml += `
          </div>
        </div>`;

        return stepHtml;
      })
      .join("");
  } else {
    methodContent =
      '<div class="method-step"><em>No method steps specified for this recipe.</em></div>';
  }
  html = html.replace(/{{METHOD_CONTENT}}/g, methodContent);

  // Process HACCP content from actual API data - ONLY if data exists
  let haccpContent = "";

  if (data.haccp_attributes && data.haccp_attributes.length > 0) {
    haccpContent = data.haccp_attributes
      .map((haccp: any) => {
        const title = haccp.attribute_title || "HACCP Control Point";
        const description =
          haccp.attribute_description || "No specific instructions provided";
        return `<div class="haccp-item"><strong>${title}</strong>${description}</div>`;
      })
      .join("");
    html = html.replace(/{{HACCP_CONTENT}}/g, haccpContent);
    html = html.replace(/{{HACCP_HIDDEN}}/g, "");
  } else {
    // Hide HACCP section completely if no data
    html = html.replace(/{{HACCP_CONTENT}}/g, "");
    html = html.replace(/{{HACCP_HIDDEN}}/g, "hidden");
  }

  // Process nutrition information from actual API data
  let nutritionTableRows = "";

  if (data.nutrition_attributes && data.nutrition_attributes.length > 0) {
    nutritionTableRows = data.nutrition_attributes
      .map((nutrition: any) => {
        const nutrientName = nutrition.attribute_title || "Nutrient";
        const per100g = nutrition.unit
          ? `${nutrition.unit}${nutrition.unit_of_measure ? ` ${nutrition.unit_of_measure}` : ""}`
          : "-";

        // Calculate per serving value based on recipe portions
        const totalPortions =
          parseFloat(data.recipe_total_portions) ||
          parseFloat(data.recipe_yield) ||
          1;
        const perServingValue = nutrition.unit
          ? (parseFloat(nutrition.unit) / totalPortions).toFixed(2)
          : "-";
        const perServing =
          perServingValue !== "-"
            ? `${perServingValue}${nutrition.unit_of_measure ? ` ${nutrition.unit_of_measure}` : ""}`
            : "-";

        return `
        <tr>
          <td style="text-align: left; font-weight: bold;">${nutrientName}</td>
          <td>${per100g}</td>
          <td>${perServing}</td>
          <td>-</td>
          <td>-</td>
        </tr>
      `;
      })
      .join("");
  } else {
    nutritionTableRows =
      '<tr><td colspan="5" style="text-align: center; font-style: italic;">No nutrition information available</td></tr>';
  }
  html = html.replace(/{{NUTRITION_TABLE_ROWS}}/g, nutritionTableRows);
  html = html.replace(
    /{{NUTRITION_HIDDEN}}/g,
    data.nutrition_attributes && data.nutrition_attributes.length > 0
      ? ""
      : "hidden"
  );

  // Process chef tips and FOH notes from actual API data
  const chefTips =
    data.recipe_head_chef_tips || "No chef tips available for this recipe.";
  const fohTips =
    data.recipe_foh_tips || "No FOH notes available for this recipe.";

  html = html.replace(/{{CHEF_TIPS}}/g, chefTips);
  html = html.replace(
    /{{CHEF_TIPS_HIDDEN}}/g,
    data.recipe_head_chef_tips ? "" : "hidden"
  );
  html = html.replace(/{{FOH_TIPS}}/g, fohTips);
  html = html.replace(
    /{{FOH_TIPS_HIDDEN}}/g,
    data.recipe_foh_tips ? "" : "hidden"
  );
  html = html.replace(
    /{{TIPS_HIDDEN}}/g,
    data.recipe_head_chef_tips || data.recipe_foh_tips ? "" : "hidden"
  );

  // Process serving instructions from actual API data
  let servingInstructions = "";
  if (data.recipe_serve_in) {
    servingInstructions += `Serve in: ${data.recipe_serve_in}`;
  }
  if (data.recipe_serving_method) {
    servingInstructions += servingInstructions
      ? `. Method: ${data.recipe_serving_method}`
      : `Method: ${data.recipe_serving_method}`;
  }
  if (data.recipe_garnish) {
    servingInstructions += servingInstructions
      ? `. Garnish: ${data.recipe_garnish}`
      : `Garnish: ${data.recipe_garnish}`;
  }
  if (!servingInstructions) {
    servingInstructions = "No specific serving instructions provided.";
  }

  html = html.replace(/{{SERVING_INSTRUCTIONS}}/g, servingInstructions);
  html = html.replace(
    /{{SERVING_HIDDEN}}/g,
    data.recipe_serve_in || data.recipe_serving_method || data.recipe_garnish
      ? ""
      : "hidden"
  );

  // Additional content sections for right column using actual API data
  const storageContent =
    "Store at appropriate temperature. Keep covered and protected from contamination.";
  const preparationContent =
    "Follow standard food safety procedures during preparation.";
  const cookingContent = `Cook to appropriate temperature. ${data.recipe_cook_time ? `Cooking time: ${data.recipe_cook_time} minutes.` : ""} Ensure food safety guidelines are followed.`;

  html = html.replace(/{{STORAGE_CONTENT}}/g, storageContent);
  html = html.replace(/{{PREPARATION_CONTENT}}/g, preparationContent);
  html = html.replace(/{{COOKING_CONTENT}}/g, cookingContent);

  // Process allergen content for detailed section
  let allergensContent = "";
  if (data.allergen_attributes) {
    const containsItems = data.allergen_attributes.contains || [];
    const mayContainItems = data.allergen_attributes.may_contain || [];

    if (containsItems.length > 0 || mayContainItems.length > 0) {
      allergensContent = '<div class="allergen-details">';
      if (containsItems.length > 0) {
        allergensContent += `<strong>Contains:</strong> ${containsItems.map((a: any) => a.attribute_title).join(", ")}<br>`;
      }
      if (mayContainItems.length > 0) {
        allergensContent += `<strong>May contain:</strong> ${mayContainItems.map((a: any) => a.attribute_title).join(", ")}<br>`;
      }
      allergensContent += "</div>";
    }
  }

  if (!allergensContent) {
    allergensContent =
      "Please check with kitchen staff for allergen information.";
  }

  html = html.replace(/{{ALLERGENS_CONTENT}}/g, allergensContent);
  html = html.replace(/{{ALLERGENS_HIDDEN}}/g, "");

  // Process resources from actual API data
  let resourcesHtml = "";

  if (data.resources && data.resources.length > 0) {
    // Group resources by type for better organization
    const imageResources: string[] = [];
    const linkResources: string[] = [];

    data.resources.forEach((resource: any) => {
      const resourceType =
        resource.item_detail?.item_type || resource.type || "file";
      const resourceLink = resource.item_detail?.item_link;

      if (resourceType.includes("image") && resourceLink) {
        imageResources.push(`
          <div class="resource-image-item">
            <img src="${resourceLink}" alt="Resource Image" class="resource-image"
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div class="image-fallback" style="display: none;">🖼️ Image not available</div>
          </div>
        `);
      } else if (resourceLink) {
        const linkIcon =
          resourceType.includes("video") ||
            resource.item_detail?.item_type === "youtube"
            ? "🎥"
            : resourceType.includes("pdf")
              ? "📄"
              : resourceType.includes("doc")
                ? "📝"
                : "🔗";
        const linkText =
          resourceType.includes("video") ||
            resource.item_detail?.item_type === "youtube"
            ? "Watch Video"
            : resourceType.includes("pdf")
              ? "View PDF"
              : resourceType.includes("doc")
                ? "View Document"
                : "Open Link";

        linkResources.push(`
          <div class="resource-link-item">
            <a href="${resourceLink}" target="_blank" rel="noopener noreferrer">
              ${linkIcon} ${linkText}
            </a>
          </div>
        `);
      }
    });

    // Combine all resources into one organized section
    resourcesHtml = `
      <div class="resources-container">
        ${imageResources.length > 0
        ? `
          <div class="resource-images">
            ${imageResources.join("")}
          </div>
        `
        : ""
      }
        ${linkResources.length > 0
        ? `
          <div class="resource-links">
            ${linkResources.join("")}
          </div>
        `
        : ""
      }
      </div>
    `;
  }
  html = html.replace(/{{RESOURCES_LIST}}/g, resourcesHtml);
  html = html.replace(/{{RESOURCES_HIDDEN}}/g, resourcesHtml ? "" : "hidden");

  // Check if nutrition content should be shown (dynamic content flow)
  const hasNutritionContent =
    data.nutrition_attributes &&
    data.nutrition_attributes.length > 0;

  // Show nutrition section if there's any nutrition data
  html = html.replace(/{{NUTRITION_HIDDEN}}/g, hasNutritionContent ? "" : "hidden");

  // Remove static page logic - content flows dynamically
  html = html.replace(/{{PAGE_2_HIDDEN}}/g, "hidden"); // Always hide static page 2
  html = html.replace(/{{TOTAL_PAGES}}/g, ""); // Will be handled by Puppeteer

  return html;
};

/**
 * Process template data using Handlebars templating engine
 * This function prepares the data structure for Handlebars template processing
 */
const processTemplateDataForHandlebars = (data: any): any => {
  // Process recipe image
  const recipeImageUrl = data.item_detail?.item_link || data.placeholderItem?.placeholderUrl || "";

  // Process timing information
  const prepTime = parseInt(data.recipe_preparation_time) || 0;
  const cookTime = parseInt(data.recipe_cook_time) || 0;
  const totalTime = prepTime + cookTime;

  // Calculate costs from ingredients
  let totalCost = 0;
  if (data.ingredients && data.ingredients.length > 0) {
    totalCost = data.ingredients.reduce((sum: number, ing: any) => {
      const cost = parseFloat(ing.ingredient_cost) || 0;
      const quantity = parseFloat(ing.ingredient_quantity) || 0;
      return sum + (cost * quantity);
    }, 0);
  }

  const totalPortions = parseFloat(data.recipe_total_portions) || parseFloat(data.recipe_yield) || 1;
  const portionCost = totalCost > 0 && totalPortions > 0 ? totalCost / totalPortions : 0;

  // Process allergen information
  let allergenIcons = "";
  let allergenText = "";

  if (data.allergen_attributes) {
    const containsAllergens = data.allergen_attributes.contains || [];
    const mayContainAllergens = data.allergen_attributes.may_contain || [];

    const allAllergens = [...containsAllergens, ...mayContainAllergens];
    if (allAllergens.length > 0) {
      allergenIcons = allAllergens
        .map((allergen: any) => {
          if (allergen.item_detail?.item_link) {
            return `<img src="${allergen.item_detail.item_link}" alt="${allergen.attribute_title}" class="allergen-icon">`;
          }
          // Map common allergens to emojis as fallback
          const iconMap: any = {
            milk: "🥛", dairy: "🥛", gluten: "🌾", wheat: "🌾", eggs: "🥚", egg: "🥚",
            nuts: "🥜", peanuts: "🥜", "tree nuts": "🥜", fish: "🐟", soy: "🫘", soya: "🫘",
            shellfish: "🦐", crustaceans: "🦐", molluscs: "🦪", sesame: "🌰", celery: "🥬",
            mustard: "🌭", sulphites: "🍷", lupin: "🌱"
          };
          const allergenName = allergen.attribute_title?.toLowerCase() || "";
          const icon = Object.keys(iconMap).find((key) => allergenName.includes(key));
          return icon ? iconMap[icon] : "⚠️";
        })
        .join(" ");

      if (containsAllergens.length > 0) {
        allergenText = `Contains: ${containsAllergens.map((a: any) => a.attribute_title).join(", ")}`;
      }
      if (mayContainAllergens.length > 0) {
        allergenText += allergenText
          ? `. May contain: ${mayContainAllergens.map((a: any) => a.attribute_title).join(", ")}`
          : `May contain: ${mayContainAllergens.map((a: any) => a.attribute_title).join(", ")}`;
      }
    }
  }

  // Process recipe tags
  let recipeTags = "";
  if (data.categories && data.categories.length > 0) {
    recipeTags = data.categories.map((cat: any) => cat.category_name).join(", ");
  }
  if (data.dietary_attributes && data.dietary_attributes.length > 0) {
    const dietaryItems = data.dietary_attributes.map((diet: any) => diet.attribute_title).join(", ");
    recipeTags = recipeTags ? `${recipeTags}, ${dietaryItems}` : dietaryItems;
  }
  if (data.cuisine_attributes && data.cuisine_attributes.length > 0) {
    const cuisineItems = data.cuisine_attributes.map((cuisine: any) => cuisine.attribute_title).join(", ");
    recipeTags = recipeTags ? `${recipeTags}, ${cuisineItems}` : cuisineItems;
  }
  if (data.recipe_complexity_level) {
    recipeTags = recipeTags ? `${recipeTags}, ${data.recipe_complexity_level} complexity` : `${data.recipe_complexity_level} complexity`;
  }
  if (data.recipe_serving_method) {
    recipeTags = recipeTags ? `${recipeTags}, ${data.recipe_serving_method}` : data.recipe_serving_method;
  }



  // Process yield information
  const cookedWeight = data.recipe_yield
    ? `${data.recipe_yield}${data.recipe_yield_unit ? ` ${data.recipe_yield_unit}` : ""}`
    : "-";
  const servingSize = data.recipe_single_portion_size ? `${data.recipe_single_portion_size}g` : "-";

  // Process ingredients
  const processedIngredients = (data.ingredients || []).map((ing: any) => {
    const preparationDetails = [];
    if (ing.ingredient_cooking_method_title) {
      preparationDetails.push(`Cooking: ${ing.ingredient_cooking_method_title}`);
    }
    if (ing.preparation_method_title) {
      preparationDetails.push(`Prep: ${ing.preparation_method_title}`);
    }
    const preparationText = preparationDetails.length > 0 ? preparationDetails.join(", ") : "-";

    return {
      ...ing,
      preparationText,
      formattedCost: ing.ingredient_cost ? `£${parseFloat(ing.ingredient_cost).toFixed(2)}` : "-"
    };
  });

  // Process method steps
  const processedSteps = (data.steps || [])
    .sort((a: any, b: any) => (a.recipe_step_order || 0) - (b.recipe_step_order || 0))
    .map((step: any, index: number) => ({
      ...step,
      stepNumber: step.recipe_step_order || index + 1,
      hasImage: !!(step.item_detail?.item_link),
      imageUrl: step.item_detail?.item_link || ""
    }));

  // Process resources
  const processedResources: {
    images: Array<{ url: string; alt: string }>;
    links: Array<{ url: string; icon: string; text: string }>;
  } = {
    images: [],
    links: []
  };

  if (data.resources && data.resources.length > 0) {
    data.resources.forEach((resource: any) => {
      const resourceLink = resource.item_detail?.item_link || resource.item_link;
      const resourceType = resource.item_detail?.item_mime_type || resource.item_link_type || resource.type || "file";

      const isImage = resourceType.includes("image") ||
        (resourceLink && (resourceLink.includes('.jpg') || resourceLink.includes('.jpeg') ||
          resourceLink.includes('.png') || resourceLink.includes('.gif') || resourceLink.includes('.webp')));

      if (isImage && resourceLink) {
        processedResources.images.push({
          url: resourceLink,
          alt: "Resource Image"
        });
      } else if (resourceLink) {
        const isVideo = resourceType.includes("video") ||
          resource.item_link_type === "youtube" ||
          resourceLink.includes('youtube.com') ||
          resourceLink.includes('youtu.be') ||
          resourceLink.includes('vimeo.com');

        const isPdf = resourceType.includes("pdf") || resourceLink.includes('.pdf');
        const isDoc = resourceType.includes("doc") || resourceType.includes("word") ||
          resourceLink.includes('.doc') || resourceLink.includes('.docx');

        processedResources.links.push({
          url: resourceLink,
          icon: isVideo ? "🎥" : isPdf ? "📄" : isDoc ? "📝" : "🔗",
          text: isVideo ? "Watch Video" : isPdf ? "View PDF" : isDoc ? "View Document" : "Open Link"
        });
      }
    });
  }

  // Process HACCP content
  const processedHaccp = (data.haccp_attributes || []).map((haccp: any) => ({
    title: haccp.attribute_title || "HACCP Control Point",
    description: haccp.attribute_description || "No specific instructions provided"
  }));

  // Process nutrition information
  const processedNutrition = (data.nutrition_attributes || []).map((nutrition: any) => {
    const per100g = nutrition.unit
      ? `${nutrition.unit}${nutrition.unit_of_measure ? ` ${nutrition.unit_of_measure}` : ""}`
      : "-";

    const perServingValue = nutrition.unit ? (parseFloat(nutrition.unit) / totalPortions).toFixed(2) : "-";
    const perServing = perServingValue !== "-"
      ? `${perServingValue}${nutrition.unit_of_measure ? ` ${nutrition.unit_of_measure}` : ""}`
      : "-";

    return {
      name: nutrition.attribute_title || "Nutrient",
      per100g,
      perServing,
      dailyValue: "-",
      notes: "-"
    };
  });

  // Process allergen content for detailed section
  let allergensContent = "";
  if (data.allergen_attributes) {
    const containsItems = data.allergen_attributes.contains || [];
    const mayContainItems = data.allergen_attributes.may_contain || [];

    if (containsItems.length > 0 || mayContainItems.length > 0) {
      allergensContent = '<div class="allergen-details">';
      if (containsItems.length > 0) {
        allergensContent += `<strong>Contains:</strong> ${containsItems.map((a: any) => a.attribute_title).join(", ")}<br>`;
      }
      if (mayContainItems.length > 0) {
        allergensContent += `<strong>May contain:</strong> ${mayContainItems.map((a: any) => a.attribute_title).join(", ")}<br>`;
      }
      allergensContent += "</div>";
    }
  }
  if (!allergensContent) {
    allergensContent = "Please check with kitchen staff for allergen information.";
  }

  // Process serving instructions
  let servingInstructions = "";
  if (data.recipe_serve_in) {
    servingInstructions += `Serve in: ${data.recipe_serve_in}`;
  }
  if (data.recipe_serving_method) {
    servingInstructions += servingInstructions
      ? `. Method: ${data.recipe_serving_method}`
      : `Method: ${data.recipe_serving_method}`;
  }
  if (data.recipe_garnish) {
    servingInstructions += servingInstructions
      ? `. Garnish: ${data.recipe_garnish}`
      : `Garnish: ${data.recipe_garnish}`;
  }
  if (!servingInstructions) {
    servingInstructions = "No specific serving instructions provided.";
  }

  // Return the original data with computed values added
  return {
    // Spread all original data
    ...data,

    // Add computed values for template
    recipe_image_url: recipeImageUrl,
    prep_time: prepTime > 0 ? `${prepTime} min` : "-",
    cook_time: cookTime > 0 ? `${cookTime} min` : "-",
    total_time: totalTime > 0 ? `${totalTime} min` : "-",
    servings: data.recipe_yield || data.recipe_total_portions || "-",

    // Cost calculations
    batch_cost: totalCost > 0 ? `£${totalCost.toFixed(2)}` : "£0.00",
    serving_cost: portionCost > 0 ? `£${portionCost.toFixed(2)}` : "£0.00",
    cooked_weight: data.recipe_yield
      ? `${data.recipe_yield}${data.recipe_yield_unit ? ` ${data.recipe_yield_unit}` : ""}`
      : "-",
    serving_size: data.recipe_single_portion_size ? `${data.recipe_single_portion_size}g` : "-",
    servings_per_batch: data.recipe_total_portions || "-",

    // Allergen and tags processing
    allergen_icons: allergenIcons || "No allergen information",
    recipe_tags: recipeTags || allergenText || "No specific information available",

    // Additional content
    storage_content: "Store at appropriate temperature. Keep covered and protected from contamination.",
    preparation_content: "Follow standard food safety procedures during preparation.",
    cooking_content: `Cook to appropriate temperature. ${data.recipe_cook_time ? `Cooking time: ${data.recipe_cook_time} minutes.` : ""} Ensure food safety guidelines are followed.`,
    allergens_content: allergensContent,
    serving_instructions: servingInstructions
  };
};

/**
 * Register Handlebars helpers for template processing
 */
const registerHandlebarsHelpers = () => {
  // Helper to check if a value exists and is not empty
  Handlebars.registerHelper('ifExists', (value: any, options: any) => {
    if (value && value !== '' && value !== 'hidden') {
      return options.fn(options.data?.root || {});
    }
    return options.inverse(options.data?.root || {});
  });

  // Helper for conditional rendering
  Handlebars.registerHelper('unless', (value: any, options: any) => {
    if (!value || value === '' || value === 'hidden') {
      return options.fn(options.data?.root || {});
    }
    return options.inverse(options.data?.root || {});
  });

  // Helper for OR logic
  Handlebars.registerHelper('or', function (a: any, b: any) {
    return a || b;
  });

  // Helper for addition
  Handlebars.registerHelper('add', function (a: any, b: any) {
    return (parseInt(a) || 0) + (parseInt(b) || 0);
  });

  // Helper for adding numbers
  Handlebars.registerHelper('add', function (a: any, b: any) {
    return (parseInt(a) || 0) + (parseInt(b) || 0);
  });

  // Helper to check if resource is an image
  Handlebars.registerHelper('isImage', function (resource: any) {
    const resourceLink = resource.item_detail?.item_link || resource.item_link;
    const resourceType = resource.item_detail?.item_mime_type || resource.item_link_type || resource.type || "file";

    return resourceType.includes("image") ||
      (resourceLink && (resourceLink.includes('.jpg') || resourceLink.includes('.jpeg') ||
        resourceLink.includes('.png') || resourceLink.includes('.gif') || resourceLink.includes('.webp')));
  });

  // Helper to get resource link
  Handlebars.registerHelper('getResourceLink', function (resource: any) {
    return resource.item_detail?.item_link || resource.item_link || "";
  });

  // Helper to get resource icon
  Handlebars.registerHelper('getResourceIcon', function (resource: any) {
    const resourceLink = resource.item_detail?.item_link || resource.item_link;
    const resourceType = resource.item_detail?.item_mime_type || resource.item_link_type || resource.type || "file";

    const isVideo = resourceType.includes("video") ||
      resource.item_link_type === "youtube" ||
      resourceLink?.includes('youtube.com') ||
      resourceLink?.includes('youtu.be') ||
      resourceLink?.includes('vimeo.com');

    const isPdf = resourceType.includes("pdf") || resourceLink?.includes('.pdf');
    const isDoc = resourceType.includes("doc") || resourceType.includes("word") ||
      resourceLink?.includes('.doc') || resourceLink?.includes('.docx');

    return isVideo ? "🎥" : isPdf ? "📄" : isDoc ? "📝" : "🔗";
  });

  // Helper to get resource text
  Handlebars.registerHelper('getResourceText', function (resource: any) {
    const resourceLink = resource.item_detail?.item_link || resource.item_link;
    const resourceType = resource.item_detail?.item_mime_type || resource.item_link_type || resource.type || "file";

    const isVideo = resourceType.includes("video") ||
      resource.item_link_type === "youtube" ||
      resourceLink?.includes('youtube.com') ||
      resourceLink?.includes('youtu.be') ||
      resourceLink?.includes('vimeo.com');

    const isPdf = resourceType.includes("pdf") || resourceLink?.includes('.pdf');
    const isDoc = resourceType.includes("doc") || resourceType.includes("word") ||
      resourceLink?.includes('.doc') || resourceLink?.includes('.docx');

    return isVideo ? "Watch Video" : isPdf ? "View PDF" : isDoc ? "View Document" : "Open Link";
  });

  // Helper to calculate per serving nutrition value
  Handlebars.registerHelper('getPerServingValue', function (nutrition: any, totalPortions: any, recipeYield: any) {
    const portions = parseFloat(totalPortions) || parseFloat(recipeYield) || 1;
    const nutritionValue = parseFloat(nutrition.unit) || 0;

    if (nutritionValue > 0 && portions > 0) {
      const perServing = (nutritionValue / portions).toFixed(2);
      return `${perServing}${nutrition.unit_of_measure ? ` ${nutrition.unit_of_measure}` : ""}`;
    }
    return "-";
  });
};

// Initialize Handlebars helpers
registerHandlebarsHelpers();

// Global browser instance for reuse (initialized lazily)
let globalBrowser: any = null;
const templateCache: Map<string, string> = new Map();
const orgDataCache: Map<string, { name: string; logo: string }> = new Map();

// Initialize browser instance on startup
const initializeBrowser = async (): Promise<any> => {
  if (!globalBrowser) {
    const puppeteer = await import("puppeteer");
    globalBrowser = await puppeteer.default.launch({
      headless: true,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
      ],
      timeout: 30000,
    });
  }
  return globalBrowser;
};

// Cache organization data to avoid repeated database calls
const getCachedOrgData = async (organizationId: string): Promise<{ name: string; logo: string }> => {
  if (orgDataCache.has(organizationId)) {
    return orgDataCache.get(organizationId)!;
  }

  let organizationName = "Recipe Management System";
  let organizationLogo = "";

  try {
    const [orgName, orgLogo] = await Promise.all([
      getOrgName(organizationId),
      getOrganizationLogo(organizationId),
    ]);

    if (orgName && orgName !== false) {
      organizationName = orgName;
    }
    if (orgLogo && orgLogo !== "") {
      organizationLogo = orgLogo;
    }
  } catch (error) {
    console.error("Error fetching organization data:", error);
  }

  const orgData = { name: organizationName, logo: organizationLogo };
  orgDataCache.set(organizationId, orgData);
  return orgData;
};

// Cache HTML templates
const getCachedTemplate = async (templateName: string): Promise<string> => {
  if (templateCache.has(templateName)) {
    return templateCache.get(templateName)!;
  }

  const path = await import("path");
  const fs = await import("fs");
  const templatePath = path.default.join(__dirname, `../templates/${templateName}`);
  const templateSource = fs.default.readFileSync(templatePath, "utf8");
  templateCache.set(templateName, templateSource);
  return templateSource;
};

/**
 * Ultra-fast PDF export using cached browser instance and optimized processing
 */
export const exportRecipeToPDF = async (recipe: any): Promise<Buffer> => {
  const startTime = Date.now();

  try {
    // Helper function to safely serialize data without circular references
    const safeSerialize = (obj: any): any => {
      const seen = new WeakSet();
      return JSON.parse(
        JSON.stringify(obj, (_key, val) => {
          if (val != null && typeof val === "object") {
            if (seen.has(val)) {
              return {};
            }
            seen.add(val);
          }
          return val;
        })
      );
    };

    // Get cached organization data for footer
    const organizationId = recipe.organization_id;
    const orgData = organizationId
      ? await getCachedOrgData(organizationId)
      : { name: "Recipe Management System", logo: "" };

    // Prepare recipe data for template using ALL actual API data
    const templateData = safeSerialize({
      // Basic recipe information
      id: recipe.id,
      recipe_title: recipe.recipe_title || "Untitled Recipe",
      recipe_public_title: recipe.recipe_public_title,
      recipe_description: recipe.recipe_description,
      recipe_preparation_time: recipe.recipe_preparation_time,
      recipe_cook_time: recipe.recipe_cook_time,
      recipe_status: recipe.recipe_status,
      recipe_serve_in: recipe.recipe_serve_in,
      recipe_complexity_level: recipe.recipe_complexity_level,
      recipe_garnish: recipe.recipe_garnish,
      recipe_head_chef_tips: recipe.recipe_head_chef_tips,
      recipe_foh_tips: recipe.recipe_foh_tips,
      recipe_impression: recipe.recipe_impression,
      recipe_yield: recipe.recipe_yield,
      recipe_yield_unit: recipe.recipe_yield_unit,
      recipe_total_portions: recipe.recipe_total_portions,
      recipe_single_portion_size: recipe.recipe_single_portion_size,
      recipe_serving_method: recipe.recipe_serving_method,
      has_recipe_public_visibility: recipe.has_recipe_public_visibility,
      has_recipe_private_visibility: recipe.has_recipe_private_visibility,
      recipe_slug: recipe.recipe_slug,
      organization_id: recipe.organization_id,
      created_by: recipe.created_by,
      updated_by: recipe.updated_by,
      created_at: recipe.created_at,
      updated_at: recipe.updated_at,

      // Process item_detail properly for main recipe image (recipe placeholder)
      item_detail:
        recipe.item_detail && recipe.item_detail.item_link
          ? {
            item_id: recipe.item_detail.item_id,
            item_type: recipe.item_detail.item_type,
            item_link: recipe.item_detail.item_link,
          }
          : recipe.p_url
            ? {
              item_id: recipe.p_id,
              item_type: recipe.p_type,
              item_link: recipe.p_url,
            }
            : {},

      // Process categories with all available data
      categories: (recipe.categories || []).map((cat: any) => ({
        id: cat.id,
        category_name: cat.category_name,
        category_slug: cat.category_slug,
        category_status: cat.category_status,
        item_detail: cat.item_detail || {},
      })),

      // Process ingredients with ALL available fields from API
      ingredients: (recipe.ingredients || []).map((ing: any) => ({
        id: ing.id,
        ingredient_name: ing.ingredient_name,
        ingredient_description: ing.ingredient_description,
        ingredient_quantity: ing.ingredient_quantity,
        ingredient_cost: ing.ingredient_cost,
        ingredient_measure: ing.ingredient_measure,
        ingredient_wastage: ing.ingredient_wastage,
        measure_id: ing.measure_id,
        measure_title: ing.measure_title,
        measure_slug: ing.measure_slug,
        ingredient_cooking_method: ing.ingredient_cooking_method,
        ingredient_cooking_method_title: ing.ingredient_cooking_method_title,
        preparation_method: ing.preparation_method,
        preparation_method_title: ing.preparation_method_title,
      })),

      // Process steps with ALL available data including images
      steps: (recipe.steps || []).map((step: any) => ({
        id: step.id,
        recipe_step_order: step.recipe_step_order,
        recipe_step_description: step.recipe_step_description,
        status: step.status,
        item_id: step.item_id,
        item_detail: step.item_detail || {},
      })),

      // Process resources with ALL available data
      resources: (recipe.resources || []).map((res: any) => ({
        id: res.id,
        type: res.type,
        status: res.status,
        item_detail: res.item_detail || {},
      })),

      // Process ALL attribute types with complete data
      nutrition_attributes: recipe.nutrition_attributes || [],
      allergen_attributes: recipe.allergen_attributes || {
        contains: [],
        may_contain: [],
      },
      dietary_attributes: recipe.dietary_attributes || [],
      cuisine_attributes: recipe.cuisine_attributes || [],
      haccp_attributes: recipe.haccp_attributes || [],

      // Additional data
      assigned_users: recipe.assigned_users || [],
      generation_date: new Date().toLocaleString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      }),
      creator_user_full_name: recipe.creator_user_full_name || "Unknown",
      updater_user_full_name: recipe.updater_user_full_name || "Unknown",
      // Organization data for footer
      organization_name: orgData.name,
      organization_logo: orgData.logo,
    });

    // Get cached HTML template
    const templateSource = await getCachedTemplate("recipe-pdf-new.html");

    // Process template data for Handlebars
    const handlebarsData = processTemplateDataForHandlebars(templateData);



    // Compile and render template using Handlebars
    const template = Handlebars.compile(templateSource);
    const html = template(handlebarsData);

    // Use cached browser instance for ultra-fast PDF generation
    const browser = await initializeBrowser();

    try {
      const page = await browser.newPage();

      // Optimized viewport and timeout settings
      await page.setViewport({ width: 1200, height: 1600 });
      page.setDefaultTimeout(15000); // Increased timeout for image loading



      // Wait for network idle to ensure images load
      await page.setContent(html, {
        waitUntil: "networkidle0", // Wait for network to be idle
        timeout: 15000, // Increased timeout
      });

      // Add CSS to ensure links are properly styled for PDF and optimize footer positioning
      await page.addStyleTag({
        content: `
          a[href] {
            color: #135e96 !important;
            text-decoration: underline !important;
            font-weight: 600 !important;
            border: none !important;
          }
          .resource-link a[href] {
            display: inline-block !important;
            padding: 4px 8px !important;
            background: #f8f9fa !important;
            border: 1px solid #135e96 !important;
            border-radius: 3px !important;
            color: #135e96 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            font-size: 10px !important;
          }
          /* Footer positioning optimizations */
          body {
            min-height: 100vh !important;
            display: flex !important;
            flex-direction: column !important;
          }
          .container {
            padding: 4px !important;
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
            min-height: calc(100vh - 10mm) !important;
          }
          .main-content {
            flex: 1 !important;
          }
          .footer {
            margin-top: auto !important;
            padding-top: 4px !important;
            flex-shrink: 0 !important;
            page-break-inside: avoid !important;
          }
          /* Additional spacing optimizations */
          .recipe-header {
            margin-bottom: 6px !important;
          }
          .content-wrapper {
            gap: 8px !important;
            page-break-inside: auto !important;
          }
          .method-step {
            margin-bottom: 4px !important;
            padding: 4px !important;
          }
          /* Ensure content flows naturally on page 1 */
          .left-column, .right-column {
            page-break-inside: auto !important;
          }
          /* Only nutrition section should force page 2 */
          .page-2 {
            page-break-before: always !important;
            min-height: calc(100vh - 10mm) !important;
          }
        `,
      });

      // Minimal wait for critical rendering (reduced from 2000ms to 50ms)
      await new Promise((resolve) => setTimeout(resolve, 50));

      // Ultra-fast PDF generation with optimized settings
      const pdfBuffer = await page.pdf({
        format: "A4",
        printBackground: true,
        margin: { top: "12mm", right: "8mm", bottom: "18mm", left: "8mm" },
        displayHeaderFooter: true,
        headerTemplate: "",
        footerTemplate: `
          <div style="width: 100%; font-size: 9pt; padding: 8pt 8mm 6pt 8mm; border-top: 0.5pt solid #2563eb; display: flex; justify-content: space-between; align-items: center; color: #64748b; background: white;">
            <div><span style="color: #1e293b;">🍽️ ${templateData.organization_name || 'Organization'}</span></div>
            <div style="text-align: right; font-size: 8pt;">
              <div>By: ${templateData.creator_user_full_name}</div>
              <div style="color: #1e293b;">Page <span class="pageNumber"></span> of <span class="totalPages"></span></div>
            </div>
          </div>
        `,
        timeout: 10000, // Reduced timeout
        tagged: false, // Disable for speed
        omitBackground: false,
        scale: 1.0,
        landscape: false,
        preferCSSPageSize: false,
      });

      const endTime = Date.now();
      console.log(`PDF generated in ${endTime - startTime}ms`);

      return Buffer.from(pdfBuffer);
    } finally {
      // Don't close the global browser - keep it alive for reuse
      // The browser will be closed when the application shuts down
    }
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw new Error(
      `Failed to generate PDF: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};

// Cleanup function to close browser on application shutdown
export const closePDFBrowser = async (): Promise<void> => {
  if (globalBrowser) {
    await globalBrowser.close();
    globalBrowser = null;
  }
};

/**
 * Get recipe by slug using ultra-fast optimized queries for millisecond performance
 * @param recipeSlug - Recipe slug
 * @param organizationId - Organization ID for filtering
 * @returns Complete recipe data with all relations
 */
export const getRecipeBySlugRaw = async (
  recipeSlug: string,
  organizationId?: string
): Promise<any> => {
  try {
    // First, get the recipe ID from the slug
    const orgFilter = organizationId
      ? `AND organization_id = '${organizationId}'`
      : "";
    const recipeIdQuery = await sequelize.query(
      `
      SELECT id FROM mo_recipe
      WHERE recipe_slug = '${recipeSlug}' ${orgFilter}
      AND recipe_status != 'deleted'
      LIMIT 1
    `,
      { type: QueryTypes.SELECT, raw: true }
    );

    if (!recipeIdQuery || recipeIdQuery.length === 0) {
      return null;
    }

    const recipeId = (recipeIdQuery[0] as any).id;

    // Use the existing getRecipeByIdRaw function with the found ID
    // This will automatically include freshness indicators
    return await getRecipeByIdRaw(recipeId, organizationId);
  } catch (error) {
    console.error("Error in getRecipeBySlugRaw:", error);
    throw error;
  }
};

/**
 * Get recipe by ID for PUBLIC access with organization settings
 * @param recipeId - Recipe ID
 * @param organizationId - Organization ID for filtering (optional for public access)
 * @returns Complete recipe data with organization settings
 */
export const getPublicRecipeByIdRaw = async (
  recipeId: number,
  organizationId?: string,
): Promise<any> => {
  try {
    // Get recipe data using existing function
    const recipeData = await getRecipeByIdRaw(recipeId, organizationId);

    if (!recipeData) {
      return null;
    }

    // Add organization settings for public access in structured format
    if (recipeData?.organization_id) {
      try {
        const organizationSettings = await settingsService.getStructuredSettingsByOrganizationId(recipeData.organization_id);
        recipeData.organization_settings = organizationSettings;
      } catch (error) {
        console.error("Error fetching organization settings for public recipe:", error);
        // Continue with default structured settings if there's an error
        recipeData.organization_settings = {
          privateRecipeVisibilitySettings: {
            highlightChanges: false
          },
          publicRecipeSettings: {
            publicStoreAccess: true
          },
          publicRecipeCallToAction: {
            contactForm: false,
            contactInfo: {
              enabled: true,
              name: "Restaurant Manager",
              phone: "******-0123",
              email: "<EMAIL>",
              link: "https://restaurant.com/contact"
            },
            customCtaLink: {
              enabled: false,
              text: "Order Now",
              link: "https://restaurant.com/order"
            },
            none: false
          },
          recipeDetailsToDisplayPublicly: {
            category: true,
            ingredients: true,
            nutritionalInformation: true,
            allergenInformation: true,
            preparationSteps: true,
            totalTime: false,
            yieldPortioning: false,
            cost: false,
            dietarySuitability: false,
            cuisineType: false,
            media: false,
            links: false,
            scale: false,
            serveIn: false,
            garnish: false,
            haccp: false,
          }
        };
      }
    } else {
      // Default structured settings when no organization_id
      recipeData.organization_settings = {
        privateRecipeVisibilitySettings: {
          highlightChanges: false
        },
        publicRecipeSettings: {
          publicStoreAccess: true
        },
        publicRecipeCallToAction: {
          contactForm: false,
          contactInfo: {
            enabled: true,
            name: "Restaurant Manager",
            phone: "******-0123",
            email: "<EMAIL>",
            link: "https://restaurant.com/contact"
          },
          customCtaLink: {
            enabled: false,
            text: "Order Now",
            link: "https://restaurant.com/order"
          },
          none: false
        },
        recipeDetailsToDisplayPublicly: {
          category: true,
          ingredients: true,
          nutritionalInformation: true,
          allergenInformation: true,
          preparationSteps: true,
          totalTime: false,
          yieldPortioning: false,
          cost: false,
          dietarySuitability: false,
          cuisineType: false,
          media: false,
          links: false,
          scale: false,
          serveIn: false,
          garnish: false,
          haccp: true,
        }
      };
    }

    return recipeData;
  } catch (error) {
    console.error("Error in getPublicRecipeByIdRaw:", error);
    throw error;
  }
};

/**
 * Get recipe by slug for PUBLIC access with organization settings
 * @param recipeSlug - Recipe slug
 * @param organizationId - Organization ID for filtering (optional for public access)
 * @returns Complete recipe data with organization settings
 */
export const getPublicRecipeBySlugRaw = async (
  recipeSlug: string,
  organizationId?: string,
): Promise<any> => {
  try {
    // First, get the recipe ID from the slug
    const orgFilter = organizationId
      ? `AND organization_id = '${organizationId}'`
      : "";
    const recipeIdQuery = await sequelize.query(
      `
      SELECT id FROM mo_recipe
      WHERE recipe_slug = '${recipeSlug}' ${orgFilter}
      AND recipe_status != 'deleted'
      LIMIT 1
    `,
      { type: QueryTypes.SELECT, raw: true },
    );

    if (!recipeIdQuery || recipeIdQuery.length === 0) {
      return null;
    }

    const recipeId = (recipeIdQuery[0] as any).id;

    // Use the public recipe function to include settings
    return await getPublicRecipeByIdRaw(recipeId, organizationId);
  } catch (error) {
    console.error("Error in getPublicRecipeBySlugRaw:", error);
    throw error;
  }
};

/**
 * Get recipe by ID using ultra-fast optimized queries for millisecond performance
 * @param recipeId - Recipe ID
 * @param organizationId - Organization ID for filtering
 * @returns Complete recipe data with all relations
 */
export const getRecipeByIdRaw = async (
  recipeId: number,
  organizationId?: string
): Promise<any> => {
  try {
    const baseUrl = getBaseUrl();
    const orgFilter = organizationId
      ? `AND r.organization_id = '${organizationId}'`
      : "";

    // Ultra-fast parallel queries with minimal data
    const [recipeData, ...relationResults] = await Promise.all([
      // Main recipe query - fastest possible with user information
      sequelize.query(
        `
        SELECT r.*,
               pi.id as p_id, pi.item_mime_type as p_type,
               CASE WHEN pi.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', pi.item_location)
                 ELSE NULL END as p_url,
               -- Creator information
               r.created_by as creator_user_id,
               (SELECT CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, ''))
                FROM nv_users WHERE id = r.created_by LIMIT 1) as creator_user_full_name,
               -- Updater information
               r.updated_by as updater_user_id,
               (SELECT CONCAT(COALESCE(user_first_name, ''), ' ', COALESCE(user_last_name, ''))
                FROM nv_users WHERE id = r.updated_by LIMIT 1) as updater_user_full_name
        FROM mo_recipe r
        LEFT JOIN nv_items pi ON r.recipe_placeholder = pi.id
        WHERE r.id = ${recipeId} ${orgFilter}
        LIMIT 1
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Categories
      sequelize.query(
        `
        SELECT 'cat' as t, c.id, c.category_name as n, c.category_slug as s, c.category_status as st,
               ci.id as i_id, ci.item_mime_type as i_type,
               CASE WHEN ci.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ci.item_location)
                 ELSE NULL END as i_url
        FROM mo_recipe_category rc
        JOIN mo_category c ON rc.category_id = c.id
        LEFT JOIN nv_items ci ON c.category_icon = ci.id
        WHERE rc.recipe_id = ${recipeId} AND rc.status = 'active' AND c.category_status = 'active'
        ORDER BY c.category_name
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Attributes
      sequelize.query(
        `
        SELECT 'attr' as t, fa.id, fa.attribute_title as n, fa.attribute_slug as s, fa.attribute_type as st,
               ai.id as i_id, ai.item_mime_type as i_type,
               CASE WHEN ai.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ai.item_location)
                 ELSE NULL END as i_url,
               ra.unit as a1, ra.unit_of_measure as a2, ra.attribute_description as a3, ra.may_contain as a4, ra.use_default as a5
        FROM mo_recipe_attributes ra
        JOIN mo_food_attributes fa ON ra.attributes_id = fa.id
        LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
        WHERE ra.recipe_id = ${recipeId} AND ra.status = 'active' AND fa.attribute_status = 'active'
        ORDER BY fa.attribute_type, fa.attribute_title
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Steps
      sequelize.query(
        `
        SELECT 'step' as t, rs.id, rs.recipe_step_description as n, rs.recipe_step_order as s, rs.status as st,
               si.id as i_id, si.item_mime_type as i_type,
               CASE WHEN si.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', si.item_location)
                 ELSE NULL END as i_url
        FROM mo_recipe_steps rs
        LEFT JOIN nv_items si ON rs.item_id = si.id
        WHERE rs.recipe_id = ${recipeId} AND rs.status = 'active'
        ORDER BY rs.recipe_step_order
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Resources
      sequelize.query(
        `
        SELECT 'res' as t, rr.id, rr.type as n, rr.item_link as s, rr.item_link_type as st,
               ri.id as i_id, rr.item_link_type as i_type,
               CASE WHEN rr.type = 'item' AND ri.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ri.item_location)
                 WHEN rr.type = 'link' AND rr.item_link IS NOT NULL
                 THEN rr.item_link
                 ELSE NULL END as i_url
        FROM mo_recipe_resources rr
        LEFT JOIN nv_items ri ON rr.item_id = ri.id
        WHERE rr.recipe_id = ${recipeId} AND rr.status = 'active'
        ORDER BY rr.id
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Ingredients
      sequelize.query(
        `
        SELECT 'ing' as t, i.id, i.ingredient_name as n, i.ingredient_slug as s, i.ingredient_status as st,
               NULL as i_id, NULL as i_type, NULL as i_url,
               ri.ingredient_quantity as a1, ri.ingredient_measure as a2, ri.ingredient_wastage as a3,
               ri.ingredient_cost as a4, rm.id as a5, rm.unit_title as a6, rm.unit_slug as a7,
               ri.ingredient_cooking_method as a8, ri.preparation_method as a9,
               i.ingredient_description as desc_field,
               cm.attribute_title as cooking_method_title, pm.attribute_title as preparation_method_title,
               i.cost_per_unit, i.waste_percentage
        FROM mo_recipe_ingredients ri
        JOIN mo_ingredients i ON ri.ingredient_id = i.id
        LEFT JOIN mo_recipe_measure rm ON ri.ingredient_measure = rm.id
        LEFT JOIN mo_food_attributes cm ON ri.ingredient_cooking_method = cm.id
        LEFT JOIN mo_food_attributes pm ON ri.preparation_method = pm.id
        WHERE ri.recipe_id = ${recipeId} AND ri.recipe_ingredient_status = 'active'
        ORDER BY i.ingredient_name
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Ingredient Categories
      sequelize.query(
        `
        SELECT 'ing_cat' as t, ic.ingredient_id as parent_id, c.id, c.category_name as n,
               c.category_slug as s, c.category_status as st,
               ci.id as i_id, ci.item_mime_type as i_type,
               CASE WHEN ci.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ci.item_location)
                 ELSE NULL END as i_url
        FROM mo_recipe_ingredients ri
        JOIN mo_ingredients_category ic ON ri.ingredient_id = ic.ingredient_id
        JOIN mo_category c ON ic.category_id = c.id
        LEFT JOIN nv_items ci ON c.category_icon = ci.id
        WHERE ri.recipe_id = ${recipeId} AND ri.recipe_ingredient_status = 'active'
          AND ic.ingredient_category_status = 'active' AND c.category_status = 'active'
        ORDER BY ic.ingredient_id, c.category_name
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Ingredient Nutrition Attributes
      sequelize.query(
        `
        SELECT 'ing_nutr' as t, ia.ingredient_id as parent_id, fa.id, fa.attribute_title as n,
               fa.attribute_slug as s, fa.attribute_type as st,
               ai.id as i_id, ai.item_mime_type as i_type,
               CASE WHEN ai.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ai.item_location)
                 ELSE NULL END as i_url,
               ia.unit as a1, ia.unit_of_measure as a2
        FROM mo_recipe_ingredients ri
        JOIN mo_ingredients_attributes ia ON ri.ingredient_id = ia.ingredient_id
        JOIN mo_food_attributes fa ON ia.attributes_id = fa.id
        LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
        WHERE ri.recipe_id = ${recipeId} AND ri.recipe_ingredient_status = 'active'
          AND ia.ingredient_attributes_status = 'active' AND fa.attribute_status = 'active'
          AND fa.attribute_type = 'nutrition'
        ORDER BY ia.ingredient_id, fa.attribute_title
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Ingredient Allergen Attributes
      sequelize.query(
        `
        SELECT 'ing_allerg' as t, ia.ingredient_id as parent_id, fa.id, fa.attribute_title as n,
               fa.attribute_slug as s, fa.attribute_type as st,
               ai.id as i_id, ai.item_mime_type as i_type,
               CASE WHEN ai.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ai.item_location)
                 ELSE NULL END as i_url
        FROM mo_recipe_ingredients ri
        JOIN mo_ingredients_attributes ia ON ri.ingredient_id = ia.ingredient_id
        JOIN mo_food_attributes fa ON ia.attributes_id = fa.id
        LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
        WHERE ri.recipe_id = ${recipeId} AND ri.recipe_ingredient_status = 'active'
          AND ia.ingredient_attributes_status = 'active' AND fa.attribute_status = 'active'
          AND fa.attribute_type = 'allergen'
        ORDER BY ia.ingredient_id, fa.attribute_title
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Ingredient Dietary Attributes
      sequelize.query(
        `
        SELECT 'ing_diet' as t, ia.ingredient_id as parent_id, fa.id, fa.attribute_title as n,
               fa.attribute_slug as s, fa.attribute_type as st,
               ai.id as i_id, ai.item_mime_type as i_type,
               CASE WHEN ai.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ai.item_location)
                 ELSE NULL END as i_url
        FROM mo_recipe_ingredients ri
        JOIN mo_ingredients_attributes ia ON ri.ingredient_id = ia.ingredient_id
        JOIN mo_food_attributes fa ON ia.attributes_id = fa.id
        LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
        WHERE ri.recipe_id = ${recipeId} AND ri.recipe_ingredient_status = 'active'
          AND ia.ingredient_attributes_status = 'active' AND fa.attribute_status = 'active'
          AND fa.attribute_type = 'dietary'
        ORDER BY ia.ingredient_id, fa.attribute_title
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Ingredient Cuisine Attributes
      sequelize.query(
        `
        SELECT 'ing_cuisine' as t, ia.ingredient_id as parent_id, fa.id, fa.attribute_title as n,
               fa.attribute_slug as s, fa.attribute_type as st,
               ai.id as i_id, ai.item_mime_type as i_type,
               CASE WHEN ai.item_location IS NOT NULL
                 THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ai.item_location)
                 ELSE NULL END as i_url
        FROM mo_recipe_ingredients ri
        JOIN mo_ingredients_attributes ia ON ri.ingredient_id = ia.ingredient_id
        JOIN mo_food_attributes fa ON ia.attributes_id = fa.id
        LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
        WHERE ri.recipe_id = ${recipeId} AND ri.recipe_ingredient_status = 'active'
          AND ia.ingredient_attributes_status = 'active' AND fa.attribute_status = 'active'
          AND fa.attribute_type = 'cuisine'
        ORDER BY ia.ingredient_id, fa.attribute_title
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),

      // Assigned Users
      sequelize.query(
        `
        SELECT 'user' as t, ru.user_id as id,
               CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, '')) as n,
               u.user_email as s, 'active' as st,
               NULL as i_id, NULL as i_type, NULL as i_url
        FROM mo_recipe_user ru
        JOIN nv_users u ON ru.user_id = u.id
        WHERE ru.recipe_id = ${recipeId} AND ru.status = 'active'
        ORDER BY u.user_first_name, u.user_last_name
      `,
        { type: QueryTypes.SELECT, raw: true }
      ),
    ]);

    // Flatten all relation results
    const relationsData = relationResults.flat();

    if (!recipeData || recipeData.length === 0) {
      return null;
    }

    // Ultra-fast response building
    const recipeResponse = buildUltraFastResponse(recipeData[0], relationsData);

    // Add freshness indicators if organizationId is provided
    // For get-by-id, we check all freshness by default since user is viewing the recipe
    if (organizationId && recipeResponse) {
      const recipeWithFreshness = await addAllFreshnessIndicators(
        recipeResponse,
        organizationId
      );
      return recipeWithFreshness;
    }

    return recipeResponse;
  } catch (error) {
    console.error("Error in getRecipeByIdRaw:", error);
    throw error;
  }
};

/**
 * Ultra-fast response builder for millisecond performance
 * Uses minimal object creation and direct property assignment
 */
const buildUltraFastResponse = (recipe: any, relations: any[]): any => {
  // Initialize response with recipe data
  const response: any = {
    id: recipe.id,
    recipe_title: recipe.recipe_title,
    recipe_public_title: recipe.recipe_public_title,
    recipe_preparation_time: recipe.recipe_preparation_time,
    recipe_cook_time: recipe.recipe_cook_time,
    has_recipe_public_visibility: recipe.has_recipe_public_visibility,
    has_recipe_private_visibility: recipe.has_recipe_private_visibility,
    recipe_status: recipe.recipe_status,
    recipe_serve_in: recipe.recipe_serve_in,
    recipe_description: recipe.recipe_description,
    recipe_complexity_level: recipe.recipe_complexity_level,
    recipe_garnish: recipe.recipe_garnish,
    recipe_head_chef_tips: recipe.recipe_head_chef_tips,
    recipe_foh_tips: recipe.recipe_foh_tips,
    recipe_impression: recipe.recipe_impression,
    recipe_yield: recipe.recipe_yield,
    recipe_yield_unit: recipe.recipe_yield_unit,
    recipe_total_portions: recipe.recipe_total_portions,
    recipe_single_portion_size: recipe.recipe_single_portion_size,
    recipe_serving_method: recipe.recipe_serving_method,
    recipe_placeholder: recipe.recipe_placeholder,
    recipe_slug: recipe.recipe_slug,
    organization_id: recipe.organization_id,
    created_by: recipe.created_by,
    updated_by: recipe.updated_by,
    created_at: recipe.created_at,
    updated_at: recipe.updated_at,
    organization_settings: recipe.organization_settings, // Include organization settings in the response
    // Vitamin fields
    vitamin_a: recipe.vitamin_a,
    vitamin_c: recipe.vitamin_c,
    calcium: recipe.calcium,
    iron: recipe.iron,
    // Cooking method flags (moved from ingredients to recipe level)
    is_ingredient_cooking_method: recipe.is_ingredient_cooking_method,
    is_preparation_method: recipe.is_preparation_method,
    // User information
    creator_user_id: recipe.creator_user_id,
    creator_user_full_name: recipe.creator_user_full_name,
    updater_user_id: recipe.updater_user_id,
    updater_user_full_name: recipe.updater_user_full_name,
    item_detail: recipe.p_id
      ? {
        item_id: recipe.p_id,
        item_type: recipe.p_type,
        item_link: recipe.p_url,
      }
      : {},
    categories: [],
    nutrition_attributes: [],
    allergen_attributes: { contains: [], may_contain: [] },
    cuisine_attributes: [],
    dietary_attributes: [],
    haccp_attributes: [],
    ingredients: [],
    steps: [],
    resources: [],
    assigned_users: [],
  };

  // Process relations in single loop for maximum speed
  for (const rel of relations) {
    switch (rel.t) {
      case "cat":
        response.categories.push({
          id: rel.id,
          category_name: rel.n,
          category_slug: rel.s,
          category_status: rel.st,
          item_detail: rel.i_id
            ? { item_id: rel.i_id, item_type: rel.i_type, item_link: rel.i_url }
            : {},
        });
        break;

      case "attr": {
        const attr: any = {
          id: rel.id,
          attribute_title: rel.n,
          attribute_slug: rel.s,
          attribute_type: rel.st,
          item_detail: rel.i_id
            ? { item_id: rel.i_id, item_type: rel.i_type, item_link: rel.i_url }
            : {},
        };

        if (rel.st === "nutrition") {
          attr.unit = rel.a1;
          attr.unit_of_measure = rel.a2;
          response.nutrition_attributes.push(attr);
        } else if (rel.st === "allergen") {
          attr.may_contain = rel.a4 || false;
          if (attr.may_contain) {
            response.allergen_attributes.may_contain.push(attr);
          } else {
            response.allergen_attributes.contains.push(attr);
          }
        } else if (rel.st === "haccp_category") {
          attr.attribute_description = rel.a3;
          attr.use_default = rel.a5;
          response.haccp_attributes.push(attr);
        } else if (rel.st === "cuisine") {
          response.cuisine_attributes.push(attr);
        } else if (rel.st === "dietary") {
          response.dietary_attributes.push(attr);
        }
        break;
      }

      case "step":
        response.steps.push({
          id: rel.id,
          recipe_step_order: parseInt(rel.s) || 0,
          recipe_step_description: rel.n,
          item_id: rel.i_id,
          status: rel.st,
          item_detail: rel.i_id
            ? { item_id: rel.i_id, item_type: rel.i_type, item_link: rel.i_url }
            : {},
        });
        break;

      case "res": {
        const resourceItem: any = {
          id: rel.id,
          type: rel.n,
          status: "active",
          item_detail: {},
        };

        // Handle item type resources (files)
        if (rel.n === "item" && rel.i_id) {
          resourceItem.item_detail = {
            item_id: rel.i_id,
            item_type: rel.i_type,
            item_link: rel.i_url,
          };
        }
        // Handle link type resources (URLs)
        else if (rel.n === "link" && rel.s) {
          resourceItem.item_detail = {
            item_id: null,
            item_type: rel.st, // item_link_type
            item_link: rel.s, // item_link
          };
        }

        response.resources.push(resourceItem);
        break;
      }

      case "ing":
        response.ingredients.push({
          id: rel.id,
          ingredient_name: rel.n,
          ingredient_slug: rel.s,
          ingredient_description: rel.desc_field,
          ingredient_status: rel.st,
          recipe_ingredient_status: "active",
          ingredient_quantity: rel.a1,
          ingredient_measure: rel.a2,
          ingredient_wastage: rel.a3,
          ingredient_cost: rel.a4,
          ingredient_cooking_method: rel.a8, // ID
          ingredient_cooking_method_title: rel.cooking_method_title, // Title
          preparation_method: rel.a9, // ID
          preparation_method_title: rel.preparation_method_title, // Title
          measure_id: rel.a5,
          measure_title: rel.a6,
          measure_slug: rel.a7,
          cost_per_unit: rel.cost_per_unit,
          waste_percentage: rel.waste_percentage,
          // Initialize arrays for related data
          categories: [],
          nutrition_attributes: [],
          allergen_attributes: [],
          dietary_attributes: [],
          cuisine_attributes: [],
        });
        break;

      case "user":
        response.assigned_users.push({
          user_id: rel.id,
          user_full_name: rel.n,
          user_email: rel.s,
        });
        break;

      case "ing_cat": {
        // Find the ingredient and add category to it
        const ingredient = response.ingredients.find((ing: any) => ing.id === rel.parent_id);
        if (ingredient) {
          ingredient.categories.push({
            id: rel.id,
            category_name: rel.n,
            category_slug: rel.s,
            category_status: rel.st,
            item_detail: rel.i_id ? {
              id: rel.i_id,
              item_mime_type: rel.i_type,
              item_location: rel.i_url || ''
            } : {},
          });
        }
        break;
      }

      case "ing_nutr": {
        // Find the ingredient and add nutrition attribute to it
        const ingredient = response.ingredients.find((ing: any) => ing.id === rel.parent_id);
        if (ingredient) {
          ingredient.nutrition_attributes.push({
            id: rel.id,
            attribute_title: rel.n,
            attribute_slug: rel.s,
            attribute_type: rel.st,
            item_detail: rel.i_id ? {
              id: rel.i_id,
              item_mime_type: rel.i_type,
              item_location: rel.i_url || ''
            } : {},
            unit: rel.a1,
            unit_of_measure: rel.a2,
          });
        }
        break;
      }

      case "ing_allerg": {
        // Find the ingredient and add allergen attribute to it
        const ingredient = response.ingredients.find((ing: any) => ing.id === rel.parent_id);
        if (ingredient) {
          ingredient.allergen_attributes.push({
            id: rel.id,
            attribute_title: rel.n,
            attribute_slug: rel.s,
            attribute_type: rel.st,
            item_detail: rel.i_id ? {
              id: rel.i_id,
              item_mime_type: rel.i_type,
              item_location: rel.i_url || ''
            } : {},
          });
        }
        break;
      }

      case "ing_diet": {
        // Find the ingredient and add dietary attribute to it
        const ingredient = response.ingredients.find((ing: any) => ing.id === rel.parent_id);
        if (ingredient) {
          ingredient.dietary_attributes.push({
            id: rel.id,
            attribute_title: rel.n,
            attribute_slug: rel.s,
            attribute_type: rel.st,
            item_detail: rel.i_id ? {
              id: rel.i_id,
              item_mime_type: rel.i_type,
              item_location: rel.i_url || ''
            } : {},
          });
        }
        break;
      }

      case "ing_cuisine": {
        // Find the ingredient and add cuisine attribute to it
        const ingredient = response.ingredients.find((ing: any) => ing.id === rel.parent_id);
        if (ingredient) {
          ingredient.cuisine_attributes.push({
            id: rel.id,
            attribute_title: rel.n,
            attribute_slug: rel.s,
            attribute_type: rel.st,
            item_detail: rel.i_id ? {
              id: rel.i_id,
              item_mime_type: rel.i_type,
              item_location: rel.i_url || ''
            } : {},
          });
        }
        break;
      }
    }
  }

  // Convert boolean fields from 0/1 to true/false before returning
  return convertBooleanFields(response, RECIPE_BOOLEAN_FIELDS);
};

/**
 * Example function demonstrating how to use getUserLiterals helper
 * This shows how to include user information in Sequelize queries
 */
export const getRecipeWithUserInfoExample = async (
  recipeId: number,
  organizationId?: string
) => {
  try {
    // Using getUserLiterals helper for clean user information fetching
    const creatorLiterals = getUserLiterals("created_by", "creator");
    const updaterLiterals = getUserLiterals("updated_by", "updater");

    // Example Sequelize query using the literals
    const recipe = await sequelize.query(
      `
      SELECT r.id,
             r.recipe_title,
             r.recipe_status,
             r.created_by,
             r.updated_by,
             r.created_at,
             r.updated_at,
             -- Basic user information using getUserLiterals
             ${creatorLiterals.creator_user_id} as creator_user_id,
             ${creatorLiterals.creator_user_full_name} as creator_user_full_name,
             ${updaterLiterals.updater_user_id} as updater_user_id,
             ${updaterLiterals.updater_user_full_name} as updater_user_full_name
      FROM mo_recipe r
      WHERE r.id = :recipeId
      ${organizationId ? "AND r.organization_id = :organizationId" : ""}
      LIMIT 1
    `,
      {
        type: QueryTypes.SELECT,
        raw: true,
        replacements: { recipeId, organizationId },
      }
    );

    return recipe[0] || null;
  } catch (error) {
    console.error("Error in getRecipeWithUserInfoExample:", error);
    throw error;
  }
};

/**
 * Example function demonstrating enhanced user literals with avatar and email
 */
export const getRecipeWithEnhancedUserInfoExample = async (
  recipeId: number
) => {
  try {
    // Enhanced user literals with additional information
    const enhancedCreatorLiterals = getEnhancedUserLiterals(
      "created_by",
      "creator"
    );
    const enhancedUpdaterLiterals = getEnhancedUserLiterals(
      "updated_by",
      "updater"
    );

    const recipe = await sequelize.query(
      `
      SELECT r.id,
             r.recipe_title,
             r.recipe_status,
             r.created_by,
             r.updated_by,
             -- Enhanced user information using getEnhancedUserLiterals
             ${enhancedCreatorLiterals.creator_user_id} as creator_user_id,
             ${enhancedCreatorLiterals.creator_user_full_name} as creator_user_full_name,
             ${enhancedCreatorLiterals.creator_user_email} as creator_user_email,
             ${enhancedCreatorLiterals.creator_user_avatar_link} as creator_user_avatar_link,
             ${enhancedUpdaterLiterals.updater_user_id} as updater_user_id,
             ${enhancedUpdaterLiterals.updater_user_full_name} as updater_user_full_name,
             ${enhancedUpdaterLiterals.updater_user_email} as updater_user_email,
             ${enhancedUpdaterLiterals.updater_user_avatar_link} as updater_user_avatar_link
      FROM mo_recipe r
      WHERE r.id = :recipeId
      LIMIT 1
    `,
      {
        type: QueryTypes.SELECT,
        raw: true,
        replacements: { recipeId },
      }
    );

    return recipe[0] || null;
  } catch (error) {
    console.error("Error in getRecipeWithEnhancedUserInfoExample:", error);
    throw error;
  }
};

/**
 * Get recipe by ID with freshness indicators
 * @param recipeId - Recipe ID
 * @param organizationId - Organization ID for filtering
 * @param options - Options to control which freshness checks to perform
 * @returns Complete recipe data with freshness indicators
 */
export const getRecipeByIdWithFreshness = async (
  recipeId: number,
  organizationId: string,
  options: {
    checkCosts?: boolean;
    checkNutrition?: boolean;
    checkAll?: boolean;
  } = { checkAll: true }
): Promise<any> => {
  try {
    // Get the recipe data using the existing ultra-fast function
    const recipeData = await getRecipeByIdRaw(recipeId, organizationId);

    if (!recipeData) {
      return null;
    }

    // Add freshness indicators based on the provided options
    const recipeWithFreshness = await addFreshnessIndicators(
      recipeData,
      organizationId,
      options
    );

    return recipeWithFreshness;
  } catch (error) {
    console.error("Error in getRecipeByIdWithFreshness:", error);
    throw error;
  }
};

/**
 * Ultra-fast optimized recipe list API with millisecond response times
 * Supports all required filters, sorting, and pagination
 * @param filters - Query parameters
 * @param userId - User ID for private endpoints (optional)
 * @param organizationId - Organization ID for filtering (optional)
 */
export const getRecipesListRaw = async (
  filters: any,
  userId?: number,
  organizationId?: string
): Promise<any> => {
  try {
    const baseUrl = getBaseUrl();

    // Extract and validate parameters with proper defaults
    const {
      page = 1,
      limit,
      size,
      search = "",
      categories = "",
      allergens = "",
      exclude_allergen = "", // New parameter to exclude recipes with specific allergens
      dietary = "",
      cuisine = "",
      portion_cost_min = "",
      portion_cost_max = "",
      cooking_time_min = "", // New parameter for minimum cooking time filter
      cooking_time_max = "", // New parameter for maximum cooking time filter
      bookmark = "",
      visibility = "", // New parameter to control visibility (private/public)
      assigned_user_id = "", // New parameter for filtering recipes assigned to specific user
      recipe_status = "", // New parameter for filtering by recipe status (published, draft, etc.)
      recipe_complexity_level = "", // New parameter for filtering by complexity level
      sort_by = "updated_at",
      sort_order = "DESC",

    } = filters;

    // Dynamic pagination - only apply if size/limit is provided
    const pageSize = size || limit;
    const validPage = Math.max(1, parseInt(String(page)) || 1);
    let validLimit = null;
    let offset = 0;

    if (pageSize) {
      validLimit = Math.max(
        1,
        Math.min(1000, parseInt(String(pageSize)) || 20)
      );
      offset = (validPage - 1) * validLimit;
    }

    // Build WHERE conditions
    const whereConditions = [];
    const joinConditions = [];
    const havingConditions = [];

    // Organization filter
    if (organizationId) {
      whereConditions.push(`r.organization_id = '${organizationId}'`);
    }
    whereConditions.push(`r.recipe_status != 'deleted'`);

    // Recipe status filter (for normal users to see only published recipes)
    if (recipe_status) {
      whereConditions.push(`r.recipe_status = '${recipe_status}'`);
    }

    // Recipe complexity level filter
    if (recipe_complexity_level) {
      whereConditions.push(
        `r.recipe_complexity_level = '${recipe_complexity_level}'`
      );
    }

    // Cooking time filter - validate numeric values
    if (cooking_time_min || cooking_time_max) {
      if (cooking_time_min && !isNaN(parseInt(cooking_time_min))) {
        whereConditions.push(
          `r.recipe_cook_time >= ${parseInt(cooking_time_min)}`
        );
      }
      if (cooking_time_max && !isNaN(parseInt(cooking_time_max))) {
        whereConditions.push(
          `r.recipe_cook_time <= ${parseInt(cooking_time_max)}`
        );
      }
    }

    // Visibility logic based on visibility parameter
    if (visibility === "private") {
      // Show private recipes only
      whereConditions.push(`r.has_recipe_private_visibility = true AND r.recipe_status = 'publish'`);
    } else if (visibility === "public") {
      // Show public recipes only
      whereConditions.push(`r.has_recipe_public_visibility = true`);
    }
    // If visibility is 'all' or not specified, show both public and private recipes
    // No additional WHERE condition needed

    // Assigned user filter (for NORMAL_USER to see only recipes assigned to them)
    if (assigned_user_id) {
      joinConditions.push(
        `INNER JOIN mo_recipe_user ru_assigned ON r.id = ru_assigned.recipe_id AND ru_assigned.user_id = ${assigned_user_id} AND ru_assigned.status = 'active'`
      );
    }

    // Bookmark filter (only for private API with user)
    if (bookmark && userId) {
      if (bookmark === "true") {
        // Use different alias if assigned_user_id is also used
        const bookmarkAlias = assigned_user_id ? "ru_bookmark" : "ru";
        joinConditions.push(
          `INNER JOIN mo_recipe_user ${bookmarkAlias} ON r.id = ${bookmarkAlias}.recipe_id AND ${bookmarkAlias}.user_id = ${userId} AND ${bookmarkAlias}.status = 'active'`
        );
      } else if (bookmark === "false") {
        // Use different alias if assigned_user_id is also used
        const bookmarkAlias = assigned_user_id ? "ru_bookmark" : "ru";
        joinConditions.push(
          `LEFT JOIN mo_recipe_user ${bookmarkAlias} ON r.id = ${bookmarkAlias}.recipe_id AND ${bookmarkAlias}.user_id = ${userId} AND ${bookmarkAlias}.status = 'active'`
        );
        whereConditions.push(`${bookmarkAlias}.recipe_id IS NULL`);
      }
    }

    // Search filter (MySQL compatible)
    if (search) {
      const searchTerm = search.replace(/'/g, "''"); // Escape single quotes
      whereConditions.push(`(
        r.recipe_title LIKE '%${searchTerm}%' OR
        r.recipe_public_title LIKE '%${searchTerm}%' OR
        r.recipe_serve_in LIKE '%${searchTerm}%' OR
        r.recipe_garnish LIKE '%${searchTerm}%'
      )`);
    }

    // Categories filter
    if (categories) {
      const categoryIds = categories
        .split(",")
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id));
      if (categoryIds.length > 0) {
        joinConditions.push(
          `INNER JOIN mo_recipe_category rc ON r.id = rc.recipe_id AND rc.category_id IN (${categoryIds.join(",")}) AND rc.status = 'active'`
        );
      }
    }

    // Allergens filter
    if (allergens) {
      const allergenIds = allergens
        .split(",")
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id));
      if (allergenIds.length > 0) {
        joinConditions.push(
          `INNER JOIN mo_recipe_attributes ra_allergen ON r.id = ra_allergen.recipe_id`
        );
        joinConditions.push(
          `INNER JOIN mo_food_attributes fa_allergen ON ra_allergen.attributes_id = fa_allergen.id AND fa_allergen.id IN (${allergenIds.join(",")}) AND fa_allergen.attribute_type = 'allergen' AND ra_allergen.status = 'active'`
        );
      }
    }

    // Exclude allergens filter (exclude recipes that contain specified allergens)
    if (exclude_allergen) {
      const excludeAllergenIds = exclude_allergen
        .split(",")
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id));
      if (excludeAllergenIds.length > 0) {
        // Use LEFT JOIN with IS NULL to exclude recipes that have these allergens
        joinConditions.push(
          `LEFT JOIN mo_recipe_attributes ra_exclude_allergen ON r.id = ra_exclude_allergen.recipe_id AND ra_exclude_allergen.status = 'active'`
        );
        joinConditions.push(
          `LEFT JOIN mo_food_attributes fa_exclude_allergen ON ra_exclude_allergen.attributes_id = fa_exclude_allergen.id AND fa_exclude_allergen.id IN (${excludeAllergenIds.join(",")}) AND fa_exclude_allergen.attribute_type = 'allergen'`
        );
        whereConditions.push(`fa_exclude_allergen.id IS NULL`);
      }
    }

    // Dietary filter
    if (dietary) {
      const dietaryIds = dietary
        .split(",")
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id));
      if (dietaryIds.length > 0) {
        joinConditions.push(
          `INNER JOIN mo_recipe_attributes ra_dietary ON r.id = ra_dietary.recipe_id`
        );
        joinConditions.push(
          `INNER JOIN mo_food_attributes fa_dietary ON ra_dietary.attributes_id = fa_dietary.id AND fa_dietary.id IN (${dietaryIds.join(",")}) AND fa_dietary.attribute_type = 'dietary' AND ra_dietary.status = 'active'`
        );
      }
    }

    // Cuisine filter
    if (cuisine) {
      const cuisineIds = cuisine
        .split(",")
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id));
      if (cuisineIds.length > 0) {
        joinConditions.push(
          `INNER JOIN mo_recipe_attributes ra_cuisine ON r.id = ra_cuisine.recipe_id`
        );
        joinConditions.push(
          `INNER JOIN mo_food_attributes fa_cuisine ON ra_cuisine.attributes_id = fa_cuisine.id AND fa_cuisine.id IN (${cuisineIds.join(",")}) AND fa_cuisine.attribute_type = 'cuisine' AND ra_cuisine.status = 'active'`
        );
      }
    }

    // Ingredient filter - NEW IMPLEMENTATION
    if (filters.ingredient) {
      const ingredientIds = filters.ingredient
        .split(",")
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id));
      if (ingredientIds.length > 0) {
        joinConditions.push(
          `INNER JOIN mo_recipe_ingredients ri_filter ON r.id = ri_filter.recipe_id AND ri_filter.ingredient_id IN (${ingredientIds.join(",")}) AND ri_filter.recipe_ingredient_status = 'active'`
        );
      }
    }

    // Exclude ingredient filter - NEW IMPLEMENTATION
    if (filters.exclude_ingredient) {
      const excludeIngredientIds = filters.exclude_ingredient
        .split(",")
        .map((id: string) => parseInt(id.trim()))
        .filter((id: number) => !isNaN(id));
      if (excludeIngredientIds.length > 0) {
        joinConditions.push(
          `LEFT JOIN mo_recipe_ingredients ri_exclude ON r.id = ri_exclude.recipe_id AND ri_exclude.ingredient_id IN (${excludeIngredientIds.join(",")}) AND ri_exclude.recipe_ingredient_status = 'active'`
        );
        whereConditions.push(`ri_exclude.recipe_id IS NULL`);
      }
    }

    // Portion cost filter - validate numeric values
    if (portion_cost_min || portion_cost_max) {
      if (portion_cost_min && !isNaN(parseFloat(portion_cost_min))) {
        havingConditions.push(
          `portion_cost >= ${parseFloat(portion_cost_min)}`
        );
      }
      if (portion_cost_max && !isNaN(parseFloat(portion_cost_max))) {
        havingConditions.push(
          `portion_cost <= ${parseFloat(portion_cost_max)}`
        );
      }
    }

    // Build sorting
    let orderBy = "";
    switch (sort_by) {
      case "alphabetical":
      case "title":
        orderBy = `r.recipe_title ${sort_order}`;
        break;
      case "portion_cost":
        orderBy = `portion_cost ${sort_order}`;
        break;
      case "created_at":
        orderBy = `r.created_at ${sort_order}`;
        break;
      case "recipe_complexity_level":
        orderBy = `r.recipe_complexity_level ${sort_order}`;
        break;
      case "updated_at":
      default:
        orderBy = `r.updated_at ${sort_order}`;
        break;
    }

    // Build the ultra-fast query
    const countQuery = `
      SELECT COUNT(DISTINCT r.id) as total
      FROM mo_recipe r
      ${joinConditions.join(" ")}
      ${whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : ""}
    `;

    const dataQuery = `
      SELECT DISTINCT
        r.id,
        r.recipe_title,
        r.recipe_public_title,
        r.recipe_status,
        r.has_recipe_public_visibility,
        r.has_recipe_private_visibility,
        r.recipe_complexity_level,
        r.recipe_description,
        r.recipe_preparation_time,
        r.recipe_cook_time,
        r.updated_at,
        r.ingredient_costs_updated_at,
        r.nutrition_values_updated_at,
        r.vitamin_a,
        r.vitamin_c,
        r.calcium,
        r.iron,
        r.is_ingredient_cooking_method,
        r.is_preparation_method,
        r.recipe_impression,
        r.recipe_slug,
        r.organization_id,
        -- Main image
        pi.id as main_image_id,
        pi.item_mime_type as main_image_type,
        CASE WHEN pi.item_location IS NOT NULL
          THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', pi.item_location)
          ELSE NULL END as main_image_url,
        -- Total cost calculation
        COALESCE((
          SELECT SUM(ri.ingredient_cost * ri.ingredient_quantity)
          FROM mo_recipe_ingredients ri
          WHERE ri.recipe_id = r.id AND ri.recipe_ingredient_status = 'active'
        ), 0) as total_cost,
        -- Total portions
        r.recipe_total_portions as total_portions,
        -- Portion cost calculation: totalCost / totalPortions (if totalPortions > 0, else 0)
        CASE
          WHEN r.recipe_total_portions > 0 THEN
            COALESCE((
              SELECT SUM(ri.ingredient_cost * ri.ingredient_quantity)
              FROM mo_recipe_ingredients ri
              WHERE ri.recipe_id = r.id AND ri.recipe_ingredient_status = 'active'
            ), 0) / r.recipe_total_portions
          ELSE 0
        END as portion_cost,
        -- Bookmark status for current user
        ${userId ? `CASE WHEN ru_bookmark.recipe_id IS NOT NULL THEN true ELSE false END as is_bookmarked,` : "false as is_bookmarked,"}
        -- Categories (simple string array)
        (
          SELECT GROUP_CONCAT(c.category_name ORDER BY c.category_name SEPARATOR '|||')
          FROM mo_recipe_category rc2
          JOIN mo_category c ON rc2.category_id = c.id
          WHERE rc2.recipe_id = r.id AND rc2.status = 'active'
        ) as categories_string,
        -- Allergens (simple delimited string)
        (
          SELECT GROUP_CONCAT(
            CONCAT(fa.attribute_title, ':::',
              CASE
                WHEN ai.item_location IS NOT NULL
                THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ai.item_location)
                ELSE 'NULL'
              END
            )
            ORDER BY fa.attribute_title SEPARATOR '|||'
          )
          FROM mo_recipe_attributes ra2
          JOIN mo_food_attributes fa ON ra2.attributes_id = fa.id
          LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
          WHERE ra2.recipe_id = r.id AND ra2.status = 'active' AND fa.attribute_type = 'allergen'
        ) as allergens_string,
        -- Assigned users (simple delimited string)
        (
          SELECT GROUP_CONCAT(
            CONCAT(ru.user_id, ':::', CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, '')))
            ORDER BY u.user_first_name, u.user_last_name SEPARATOR '|||'
          )
          FROM mo_recipe_user ru
          JOIN nv_users u ON ru.user_id = u.id
          WHERE ru.recipe_id = r.id AND ru.status = 'active' AND u.user_status NOT IN ('deleted', 'cancelled')
        ) as assigned_users_string
      FROM mo_recipe r
      LEFT JOIN nv_items pi ON r.recipe_placeholder = pi.id
      ${userId ? `LEFT JOIN mo_recipe_user ru_bookmark ON r.id = ru_bookmark.recipe_id AND ru_bookmark.user_id = ${userId} AND ru_bookmark.status = 'active'` : ""}
      ${joinConditions.join(" ")}
      ${whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : ""}
      ${havingConditions.length > 0 ? `HAVING ${havingConditions.join(" AND ")}` : ""}
      ORDER BY ${orderBy}
      ${validLimit ? `LIMIT ${validLimit} OFFSET ${offset}` : ""}
    `;
    // Set a very high GROUP_CONCAT limit
    await sequelize.query('SET SESSION group_concat_max_len = 10000000;');


    // Execute both queries in parallel for maximum speed
    let countResult, dataResult;
    try {
      [countResult, dataResult] = await Promise.all([
        sequelize.query(countQuery, { type: QueryTypes.SELECT, raw: true }),
        sequelize.query(dataQuery, { type: QueryTypes.SELECT, raw: true }),
      ]);
    } catch (sqlError) {
      console.error("SQL Query Error:", sqlError);
      console.error("Count Query:", countQuery);
      console.error("Data Query:", dataQuery);
      throw new Error(`SQL Query failed: ${sqlError}`);
    }

    const total = parseInt((countResult[0] as any)?.total || "0");
    const total_pages = validLimit ? Math.ceil(total / validLimit) : 0;

    // Check if dataResult is valid
    if (!dataResult || !Array.isArray(dataResult)) {
      return {
        status: true,
        message:
          visibility === "private"
            ? "Private recipes retrieved successfully"
            : "Public recipes retrieved successfully",
        count: 0,
        data: [],
        page: validPage,
        size: validLimit || 0,
        total_pages: 0,
      };
    }

    // Process results for final response - ONLY the 7 requested fields
    const recipes = dataResult.map((recipe: any) => {
      if (!recipe || typeof recipe !== 'object') {
        console.warn("Invalid recipe data:", recipe);
        return {
          id: null,
          recipe_placeholder: {},
          recipe_status: '',
          has_recipe_public_visibility: false,
          has_recipe_private_visibility: false,
          categories: [],
          recipe_title: '',
          recipe_public_title: '',
          recipe_description: '',
          allergens: [],
          total_cost: 0,
          total_portions: 0,
          cost_per_portion: 0,
          updated_at: null,
          is_bookmarked: false,
          recipe_complexity_level: '',
          assigned_users: [],
          recipe_preparation_time: 0,
          recipe_cook_time: 0,
          ingredient_costs_updated_at: null,
          nutrition_values_updated_at: null,
          vitamin_a: null,
          vitamin_c: null,
          calcium: null,
          iron: null,
          is_ingredient_cooking_method: false,
          is_preparation_method: false,
          recipe_impression: 0,
          recipe_slug: '',
          organization_id: recipe.organization_id || null,
        };
      }

      return {
        id: recipe.id,
        // 1. Main Image
        recipe_placeholder: recipe.main_image_id
          ? {
            item_id: recipe.main_image_id,
            item_type: recipe.main_image_type,
            item_link: recipe.main_image_url,
          }
          : {},
        recipe_status: recipe.recipe_status,
        has_recipe_public_visibility: recipe.has_recipe_public_visibility,
        has_recipe_private_visibility: recipe.has_recipe_private_visibility,
        // 2. Categories (array)
        categories: recipe.categories_string
          ? recipe.categories_string.split('|||').filter(Boolean)
          : [],
        // 3. Title (using recipe_title as primary, fallback to recipe_public_title)
        recipe_title: recipe.recipe_title || "",
        recipe_public_title: recipe.recipe_public_title || "",
        // 4. Description
        recipe_description: recipe.recipe_description || "",
        // 5. Allergens (icon URLs array)
        allergens: recipe.allergens_string
          ? recipe.allergens_string.split('|||').filter(Boolean).map((item: string) => {
            if (!item || typeof item !== 'string') {
              console.warn("Invalid allergen item:", item);
              return { title: '', icon: null };
            }
            const [title, icon] = item.split(':::');
            return {
              title: title || '',
              icon: icon === 'NULL' ? null : icon
            };
          })
          : [],
        // 6. Cost Calculations (with 2 decimal places)
        total_cost: parseFloat(parseFloat(recipe.total_cost || "0").toFixed(2)),
        total_portions: parseFloat(
          parseInt(recipe.total_portions || "0").toFixed(2)
        ),
        cost_per_portion: parseFloat(
          parseFloat(recipe.portion_cost || "0").toFixed(2)
        ),

        // 7. Update date
        updated_at: recipe.updated_at,
        // 8. Bookmark status
        is_bookmarked: recipe.is_bookmarked,
        // 9. Complexity level
        recipe_complexity_level: recipe.recipe_complexity_level,
        // 10. Assigned users
        assigned_users: recipe.assigned_users_string
          ? recipe.assigned_users_string
            .split('|||')
            .filter(Boolean)
            .map((item: string) => {
              if (!item || typeof item !== 'string') {
                console.warn("Invalid assigned user item:", item);
                return 0;
              }
              const userId = parseInt(item.split(':::')[0]);
              return isNaN(userId) ? 0 : userId;
            })
            .filter((id: number) => id > 0)
          : [],
        // 11. Preparation time
        recipe_preparation_time: recipe.recipe_preparation_time,
        // 12. Cook time
        recipe_cook_time: recipe.recipe_cook_time,
        // 13. Timestamp fields for freshness checking
        ingredient_costs_updated_at: recipe.ingredient_costs_updated_at,
        nutrition_values_updated_at: recipe.nutrition_values_updated_at,
        // 14. Nutrition fields
        vitamin_a: recipe.vitamin_a,
        vitamin_c: recipe.vitamin_c,
        calcium: recipe.calcium,
        iron: recipe.iron,
        // 15. Cooking method flags
        is_ingredient_cooking_method: recipe.is_ingredient_cooking_method,
        is_preparation_method: recipe.is_preparation_method,
        // 16. Impressions
        recipe_impression: recipe.recipe_impression,
        // 17. Slug
        recipe_slug: recipe.recipe_slug,
        organization_id: recipe.organization_id || null,
      };
    });

    // Convert boolean fields from 0/1 to true/false
    let recipesWithProperBooleans = recipes;
    try {
      recipesWithProperBooleans = convertBooleanFields(recipes, RECIPE_BOOLEAN_FIELDS);
    } catch (booleanError) {
      console.error("Error converting boolean fields:", booleanError);
      // Use the data as-is if conversion fails
    }

    const organization_id = recipesWithProperBooleans[0].organization_id ? recipesWithProperBooleans[0].organization_id : organizationId;

    const orgName = organization_id ? await getOrgName(organization_id) : "";
    const orgLogo = organization_id ? await getOrganizationLogo(organization_id) : "";

    return {
      status: true,
      message:
        visibility === "private"
          ? "Private recipes retrieved successfully"
          : "Public recipes retrieved successfully",
      count: total,
      data: recipesWithProperBooleans,
      page: validPage,
      size: validLimit || total,
      total_pages: total_pages,
      organization_details: {
        name: orgName,
        logo: orgLogo
      }
    };
  } catch (error) {
    console.error("Error in getRecipesListRaw:", error);
    throw error;
  }
};

/**
 * Get single recipe in the same format as recipe list API
 * Used for consistent response format across different endpoints
 * @param recipeId - Recipe ID
 * @param userId - User ID for bookmark status (optional)
 * @param organizationId - Organization ID for filtering
 * @returns Recipe data in list format with boolean conversion
 */
export const getRecipeInListFormat = async (
  recipeId: number,
  userId?: number,
  organizationId?: string
): Promise<any> => {
  try {
    const baseUrl = getBaseUrl();

    // Organization filter
    const orgFilter = organizationId ? `AND r.organization_id = '${organizationId}'` : "";

    // Build the query to get single recipe in list format
    const dataQuery = `
      SELECT DISTINCT
        r.id,
        r.recipe_title,
        r.recipe_public_title,
        r.recipe_status,
        r.has_recipe_public_visibility,
        r.has_recipe_private_visibility,
        r.recipe_complexity_level,
        r.recipe_description,
        r.recipe_preparation_time,
        r.recipe_cook_time,
        r.updated_at,
        r.ingredient_costs_updated_at,
        r.nutrition_values_updated_at,
        r.vitamin_a,
        r.vitamin_c,
        r.calcium,
        r.iron,
        r.is_ingredient_cooking_method,
        r.is_preparation_method,
        r.recipe_impression,
        r.recipe_slug,
        -- Main image
        pi.id as main_image_id,
        pi.item_mime_type as main_image_type,
        CASE WHEN pi.item_location IS NOT NULL
          THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', pi.item_location)
          ELSE NULL END as main_image_url,
        -- Total cost calculation
        COALESCE((
          SELECT SUM(ri.ingredient_cost * ri.ingredient_quantity)
          FROM mo_recipe_ingredients ri
          WHERE ri.recipe_id = r.id AND ri.recipe_ingredient_status = 'active'
        ), 0) as total_cost,
        -- Total portions
        r.recipe_total_portions as total_portions,
        -- Portion cost calculation
        CASE
          WHEN r.recipe_total_portions > 0 THEN
            COALESCE((
              SELECT SUM(ri.ingredient_cost * ri.ingredient_quantity)
              FROM mo_recipe_ingredients ri
              WHERE ri.recipe_id = r.id AND ri.recipe_ingredient_status = 'active'
            ), 0) / r.recipe_total_portions
          ELSE 0
        END as portion_cost,
        -- Bookmark status for current user
        ${userId ? `CASE WHEN ru_bookmark.recipe_id IS NOT NULL THEN true ELSE false END as is_bookmarked,` : "false as is_bookmarked,"}
        -- Categories
        (
          SELECT GROUP_CONCAT(c.category_name ORDER BY c.category_name SEPARATOR '|||')
          FROM mo_recipe_category rc2
          JOIN mo_category c ON rc2.category_id = c.id
          WHERE rc2.recipe_id = r.id AND rc2.status = 'active'
        ) as categories_string,
        -- Allergens
        (
          SELECT GROUP_CONCAT(
            CONCAT(fa.attribute_title, ':::',
              CASE
                WHEN ai.item_location IS NOT NULL
                THEN CONCAT('${baseUrl}/backend-api/v1/public/user/get-file?location=', ai.item_location)
                ELSE 'NULL'
              END
            )
            ORDER BY fa.attribute_title SEPARATOR '|||'
          )
          FROM mo_recipe_attributes ra2
          JOIN mo_food_attributes fa ON ra2.attributes_id = fa.id
          LEFT JOIN nv_items ai ON fa.attribute_icon = ai.id
          WHERE ra2.recipe_id = r.id AND ra2.status = 'active' AND fa.attribute_type = 'allergen'
        ) as allergens_string,
        -- Assigned users
        (
          SELECT GROUP_CONCAT(
            CONCAT(ru.user_id, ':::', CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, '')))
            ORDER BY u.user_first_name, u.user_last_name SEPARATOR '|||'
          )
          FROM mo_recipe_user ru
          JOIN nv_users u ON ru.user_id = u.id
          WHERE ru.recipe_id = r.id AND ru.status = 'active'
        ) as assigned_users_string
      FROM mo_recipe r
      LEFT JOIN nv_items pi ON r.recipe_placeholder = pi.id
      ${userId ? `LEFT JOIN mo_recipe_user ru_bookmark ON r.id = ru_bookmark.recipe_id AND ru_bookmark.user_id = ${userId} AND ru_bookmark.status = 'active'` : ""}
      WHERE r.id = ${recipeId} ${orgFilter}
      AND r.recipe_status != 'deleted'
      LIMIT 1
    `;

    const dataResult = await sequelize.query(dataQuery, {
      type: QueryTypes.SELECT,
      raw: true
    });

    if (!dataResult || dataResult.length === 0) {
      return null;
    }

    const recipe = dataResult[0] as any;

    // Process result in the same format as recipe list
    const processedRecipe = {
      id: recipe.id,
      // Main Image
      recipe_placeholder: recipe.main_image_id
        ? {
          item_id: recipe.main_image_id,
          item_type: recipe.main_image_type,
          item_link: recipe.main_image_url,
        }
        : {},
      recipe_status: recipe.recipe_status,
      has_recipe_public_visibility: recipe.has_recipe_public_visibility,
      has_recipe_private_visibility: recipe.has_recipe_private_visibility,
      // Categories (array)
      categories: recipe.categories_string
        ? recipe.categories_string.split('|||').filter(Boolean)
        : [],
      // Titles
      recipe_title: recipe.recipe_title || "",
      recipe_public_title: recipe.recipe_public_title || "",
      // Description
      recipe_description: recipe.recipe_description || "",
      // Allergens (icon URLs array)
      allergens: recipe.allergens_string
        ? recipe.allergens_string.split('|||').filter(Boolean).map((item: string) => {
          const [title, icon] = item.split(':::');
          return {
            title: title,
            icon: icon === 'NULL' ? null : icon
          };
        })
        : [],
      // Cost Calculations (with 2 decimal places)
      total_cost: parseFloat(parseFloat(recipe.total_cost || "0").toFixed(2)),
      total_portions: parseFloat(
        parseInt(recipe.total_portions || "0").toFixed(2)
      ),
      cost_per_portion: parseFloat(
        parseFloat(recipe.portion_cost || "0").toFixed(2)
      ),
      // Update date
      updated_at: recipe.updated_at,
      // Bookmark status
      is_bookmarked: recipe.is_bookmarked,
      // Complexity level
      recipe_complexity_level: recipe.recipe_complexity_level,
      // Assigned users
      assigned_users: recipe.assigned_users_string
        ? recipe.assigned_users_string
          .split('|||')
          .filter(Boolean)
          .map((item: string) => parseInt(item.split(':::')[0]))
        : [],
      // Preparation time
      recipe_preparation_time: recipe.recipe_preparation_time,
      // Cook time
      recipe_cook_time: recipe.recipe_cook_time,
      // Timestamp fields for freshness checking
      ingredient_costs_updated_at: recipe.ingredient_costs_updated_at,
      nutrition_values_updated_at: recipe.nutrition_values_updated_at,
      // Nutrition fields
      vitamin_a: recipe.vitamin_a,
      vitamin_c: recipe.vitamin_c,
      calcium: recipe.calcium,
      iron: recipe.iron,
      // Cooking method flags
      is_ingredient_cooking_method: recipe.is_ingredient_cooking_method,
      is_preparation_method: recipe.is_preparation_method,
      // Impressions
      recipe_impression: recipe.recipe_impression,
      // Slug
      recipe_slug: recipe.recipe_slug,
    };

    // Convert boolean fields from 0/1 to true/false
    const recipeWithProperBooleans = convertBooleanFields([processedRecipe], RECIPE_BOOLEAN_FIELDS);

    return recipeWithProperBooleans[0];
  } catch (error) {
    console.error("Error in getRecipeInListFormat:", error);
    throw error;
  }
};
